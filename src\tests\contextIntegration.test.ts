/**
 * CONTEXT INTEGRATION TESTS
 *
 * Comprehensive tests to verify that the context integration is working properly.
 * Tests the complete flow from patient context loading to agent response generation.
 */

// Vitest globals are available via vitest.config.js globals: true
import { enhancedPatientContextService } from '../services/EnhancedPatientContextService';
import { contextAssemblyService } from '../services/ContextAssemblyService';
import { AgentOrchestrator } from '../services/AgentOrchestrator';
import { vectorSearchService } from '../services/VectorSearchService';
import { aiOrchestrator } from '../services/aiOrchestrator';
import GeneralPractitionerAgent from '../agents/GeneralPractitionerAgent';
import { memoryManager } from '../services/MemoryManager';
import { supabase } from '../utils/supabaseClient';

// Mock data for testing
const mockUserId = 'test-user-123';
const mockSessionId = 'test-session-456';

// Create AgentOrchestrator instance for testing
const agentOrchestrator = new AgentOrchestrator();

const mockUserProfile = {
  id: mockUserId,
  full_name: '<PERSON>',
  date_of_birth: '1985-06-15',
  gender: 'male',
  country: 'GH',
  city: 'Accra',
  occupation: 'Teacher',
  insurance_status: 'basic',
  preferred_language: 'English'
};

const mockMedicalConditions = [
  {
    id: 'condition-1',
    user_id: mockUserId,
    condition_name: 'Hypertension',
    diagnosed_date: '2020-03-15',
    is_current: true,
    severity: 'moderate'
  },
  {
    id: 'condition-2',
    user_id: mockUserId,
    condition_name: 'Type 2 Diabetes',
    diagnosed_date: '2019-08-20',
    is_current: true,
    severity: 'well_controlled'
  }
];

const mockMedications = [
  {
    id: 'med-1',
    user_id: mockUserId,
    medication_name: 'Lisinopril',
    dosage: '10mg',
    frequency: 'once daily',
    is_current: true
  },
  {
    id: 'med-2',
    user_id: mockUserId,
    medication_name: 'Metformin',
    dosage: '500mg',
    frequency: 'twice daily',
    is_current: true
  }
];

const mockRegionalHealthData = {
  country_code: 'GH',
  country_name: 'Ghana',
  common_conditions: ['Malaria', 'Hypertension', 'Diabetes'],
  endemic_diseases: ['Malaria', 'Yellow fever'],
  current_season: 'dry_season',
  healthcare_access_level: 'limited',
  traditional_medicine: ['Traditional herbal medicine'],
  emergency_contacts: {
    hospitals: [
      { name: 'Korle Bu Teaching Hospital', phone: '+233 30 2665401', location: 'Accra' }
    ]
  },
  cultural_considerations: ['Family involvement in healthcare']
};

describe('Context Integration System', () => {
  beforeAll(async () => {
    // Initialize test environment
    console.log('🧪 Setting up context integration tests...');
    
    // Mock Supabase responses
    vi.spyOn(supabase, 'from').mockImplementation((table: string) => {
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn(),
        order: vi.fn().mockReturnThis()
      };

      switch (table) {
        case 'user_profiles':
          mockQuery.single.mockResolvedValue({ data: mockUserProfile, error: null });
          break;
        case 'medical_conditions':
          mockQuery.single.mockResolvedValue({ data: mockMedicalConditions, error: null });
          break;
        case 'medications':
          mockQuery.single.mockResolvedValue({ data: mockMedications, error: null });
          break;
        default:
          mockQuery.single.mockResolvedValue({ data: null, error: null });
      }

      return mockQuery;
    });

    // Mock RPC function for regional context
    vi.spyOn(supabase, 'rpc').mockImplementation((functionName: string) => {
      if (functionName === 'get_user_regional_context') {
        return Promise.resolve({ data: [mockRegionalHealthData], error: null });
      }
      return Promise.resolve({ data: [], error: null });
    });

    // Initialize agent orchestrator
    await agentOrchestrator.initialize();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  describe('Enhanced Patient Context Service', () => {
    test('should load comprehensive patient context', async () => {
      const context = await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);

      expect(context).toBeDefined();
      expect(context.patientProfile).toBeDefined();
      expect(context.medicalHistory).toBeDefined();
      expect(context.regionalContext).toBeDefined();

      // Verify patient profile
      expect(context.patientProfile.fullName).toBe('John Doe');
      expect(context.patientProfile.age).toBe(39); // Calculated from DOB
      expect(context.patientProfile.country).toBe('GH');
      expect(context.patientProfile.city).toBe('Accra');

      // Verify medical history
      expect(context.medicalHistory.conditions).toHaveLength(2);
      expect(context.medicalHistory.medications).toHaveLength(2);
      expect(context.medicalHistory.chronicConditions).toContain('Hypertension');
      expect(context.medicalHistory.chronicConditions).toContain('Type 2 Diabetes');

      // Verify regional context
      expect(context.regionalContext.countryCode).toBe('GH');
      expect(context.regionalContext.countryName).toBe('Ghana');
      expect(context.regionalContext.commonConditions).toContain('Malaria');
      expect(context.regionalContext.endemicDiseases).toContain('Yellow fever');
    });

    test('should cache patient context for performance', async () => {
      // First call
      const startTime1 = Date.now();
      await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);
      const duration1 = Date.now() - startTime1;

      // Second call (should be cached)
      const startTime2 = Date.now();
      await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);
      const duration2 = Date.now() - startTime2;

      // Cached call should be significantly faster
      expect(duration2).toBeLessThan(duration1 / 2);
    });
  });

  describe('Context Assembly Service', () => {
    test('should assemble comprehensive context blocks', async () => {
      const patientContext = await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);
      const conversationHistory = [
        {
          id: '1',
          session_id: mockSessionId,
          speaker_type: 'user' as const,
          speaker_name: 'user',
          content: 'I have been experiencing chest pain lately',
          timestamp: new Date().toISOString(),
          sequence_number: 1
        }
      ];

      const assembledContext = await contextAssemblyService.assembleContext(
        patientContext,
        conversationHistory,
        'I have been experiencing chest pain lately',
        {
          urgencyLevel: 'high',
          includeRegionalContext: true,
          includeCulturalContext: true
        }
      );

      expect(assembledContext).toBeDefined();
      expect(assembledContext.systemPromptAddition).toContain('PATIENT PROFILE');
      expect(assembledContext.systemPromptAddition).toContain('John Doe');
      expect(assembledContext.systemPromptAddition).toContain('Ghana');
      expect(assembledContext.systemPromptAddition).toContain('Hypertension');
      expect(assembledContext.systemPromptAddition).toContain('Lisinopril');

      // Verify context blocks
      expect(assembledContext.contextBlock.patientProfile).toContain('John Doe');
      expect(assembledContext.contextBlock.medicalHistory).toContain('Hypertension');
      expect(assembledContext.contextBlock.regionalContext).toContain('Ghana');
      expect(assembledContext.contextBlock.conversationContext).toContain('chest pain');

      // Verify priority flags for chest pain
      expect(assembledContext.priorityFlags).toContain('EMERGENCY_CONSULTATION');

      // Verify token usage
      expect(assembledContext.tokenUsage.contextTokens).toBeGreaterThan(0);
      expect(assembledContext.tokenUsage.remainingTokens).toBeGreaterThan(0);
    });

    test('should optimize context for token limits', async () => {
      const patientContext = await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);
      const conversationHistory = [];

      const assembledContext = await contextAssemblyService.assembleContext(
        patientContext,
        conversationHistory,
        'Simple question',
        {
          maxTokens: 500, // Very low limit to force optimization
          urgencyLevel: 'low'
        }
      );

      expect(assembledContext.tokenUsage.contextTokens).toBeLessThanOrEqual(500);
      expect(assembledContext.tokenUsage.optimizationApplied).toBe(true);
    });
  });

  describe('Agent Orchestrator Context Integration', () => {
    test('should pass comprehensive context to agents', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I have been experiencing chest pain and shortness of breath',
        userId: mockUserId,
        urgencyLevel: 'high'
      });

      expect(orchestrationResponse).toBeDefined();
      expect(orchestrationResponse.agentResponse).toBeDefined();
      expect(orchestrationResponse.agentResponse.content).toBeDefined();

      // Verify agent received context
      const agentResponse = orchestrationResponse.agentResponse;
      expect(agentResponse.content).toContain('John'); // Should use patient name
      
      // For high urgency chest pain, should detect emergency
      expect(agentResponse.emergencyFlags).toBeDefined();
      expect(agentResponse.emergencyFlags.length).toBeGreaterThan(0);
    });

    test('should handle context-aware agent selection', async () => {
      // Test with cardiovascular symptoms - should route to cardiologist
      const cardioResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I have severe chest pain and palpitations',
        userId: mockUserId,
        urgencyLevel: 'high'
      });

      expect(cardioResponse.agentResponse.suggestedHandoffs).toBeDefined();
      const handoffs = cardioResponse.agentResponse.suggestedHandoffs || [];
      const cardioHandoff = handoffs.find(h => h.targetAgentRole === 'cardiologist');
      expect(cardioHandoff).toBeDefined();
    });
  });

  describe('Regional Health Context Integration', () => {
    test('should include regional health considerations in responses', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I have a fever and headache',
        userId: mockUserId,
        urgencyLevel: 'medium'
      });

      const agentResponse = orchestrationResponse.agentResponse;
      
      // For fever in Ghana, should consider malaria
      expect(agentResponse.content.toLowerCase()).toMatch(/malaria|endemic|ghana/);
    });

    test('should consider seasonal health patterns', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I have a persistent cough',
        userId: mockUserId,
        urgencyLevel: 'low'
      });

      const agentResponse = orchestrationResponse.agentResponse;
      
      // During dry season in Ghana, should consider respiratory infections
      expect(agentResponse.content.toLowerCase()).toMatch(/season|respiratory|dry/);
    });
  });

  describe('Medical History Integration', () => {
    test('should reference patient medical history in responses', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I want to discuss my blood pressure',
        userId: mockUserId,
        urgencyLevel: 'low'
      });

      const agentResponse = orchestrationResponse.agentResponse;
      
      // Should reference existing hypertension diagnosis
      expect(agentResponse.content.toLowerCase()).toMatch(/hypertension|blood pressure|lisinopril/);
    });

    test('should consider medication interactions', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'Can I take ibuprofen for my headache?',
        userId: mockUserId,
        urgencyLevel: 'low'
      });

      const agentResponse = orchestrationResponse.agentResponse;
      
      // Should consider current medications (Lisinopril, Metformin)
      expect(agentResponse.content.toLowerCase()).toMatch(/medication|lisinopril|metformin|interaction/);
    });
  });

  describe('Performance and Caching', () => {
    test('should maintain acceptable response times with context', async () => {
      const startTime = Date.now();
      
      await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'How are you today?',
        userId: mockUserId,
        urgencyLevel: 'low'
      });

      const responseTime = Date.now() - startTime;
      
      // Should respond within 2 seconds even with full context loading
      expect(responseTime).toBeLessThan(2000);
    });

    test('should cache context for subsequent requests', async () => {
      // First request
      const startTime1 = Date.now();
      await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'First message',
        userId: mockUserId,
        urgencyLevel: 'low'
      });
      const duration1 = Date.now() - startTime1;

      // Second request (should use cached context)
      const startTime2 = Date.now();
      await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'Second message',
        userId: mockUserId,
        urgencyLevel: 'low'
      });
      const duration2 = Date.now() - startTime2;

      // Second request should be faster due to caching
      expect(duration2).toBeLessThan(duration1);
    });
  });

  describe('Error Handling and Fallbacks', () => {
    test('should handle missing patient context gracefully', async () => {
      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: 'new-session',
        userMessage: 'Hello',
        userId: 'non-existent-user',
        urgencyLevel: 'low'
      });

      expect(orchestrationResponse).toBeDefined();
      expect(orchestrationResponse.agentResponse).toBeDefined();
      expect(orchestrationResponse.agentResponse.content).toBeDefined();

      // Should still provide a response even without context
      expect(orchestrationResponse.agentResponse.content.length).toBeGreaterThan(0);
    });

    test('should handle context loading failures', async () => {
      // Mock a context loading failure
      vi.spyOn(enhancedPatientContextService, 'loadPatientContext')
        .mockRejectedValueOnce(new Error('Database connection failed'));

      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'Hello',
        userId: mockUserId,
        urgencyLevel: 'low'
      });

      expect(orchestrationResponse).toBeDefined();
      expect(orchestrationResponse.agentResponse).toBeDefined();

      // Should fallback to basic response
      expect(orchestrationResponse.agentResponse.content).toBeDefined();
    });
  });

  describe('NEW: Context Integration Fixes Validation', () => {
    test('should pass context to AI orchestrator in request payload', async () => {
      // Mock AI orchestrator to capture the request
      let capturedRequest: any = null;
      const originalGenerateResponse = aiOrchestrator.generateResponse;

      vi.spyOn(aiOrchestrator, 'generateResponse').mockImplementation(async (options) => {
        capturedRequest = options;
        return {
          success: true,
          data: {
            content: 'Mocked AI response with context',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 1000
          },
          timestamp: new Date().toISOString()
        };
      });

      // Create GP agent and process request
      const gpAgent = new GeneralPractitionerAgent(memoryManager);
      await gpAgent.initialize();

      const patientContext = await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);
      const assembledContext = await contextAssemblyService.assembleContext(
        patientContext,
        [],
        'I have chest pain',
        { urgencyLevel: 'high' }
      );

      const agentRequest = {
        sessionId: mockSessionId,
        userMessage: 'I have chest pain',
        conversationHistory: [],
        patientContext,
        assembledContext,
        urgencyLevel: 'high' as const
      };

      await gpAgent.handleMessage(agentRequest);

      // Verify that context was passed to AI orchestrator
      expect(capturedRequest).toBeDefined();
      expect(capturedRequest.patientContext).toBeDefined();
      expect(capturedRequest.assembledContext).toBeDefined();
      expect(capturedRequest.urgencyLevel).toBe('high');

      // Verify the system prompt includes patient context
      expect(capturedRequest.messages).toBeDefined();
      expect(capturedRequest.messages.length).toBeGreaterThan(0);

      const systemMessage = capturedRequest.messages.find((msg: any) => msg.role === 'system');
      expect(systemMessage).toBeDefined();
      expect(systemMessage.content).toContain('PATIENT CONTEXT');
      expect(systemMessage.content).toContain('John Doe');
      expect(systemMessage.content).toContain('Hypertension');

      // Restore original method
      aiOrchestrator.generateResponse = originalGenerateResponse;
    });

    test('should create simplified context for LLM consumption', async () => {
      const patientContext = await enhancedPatientContextService.loadPatientContext(mockUserId, mockSessionId);

      const simplifiedContext = await contextAssemblyService.assembleSimplifiedContext(
        patientContext,
        [],
        'I have chest pain',
        {
          maxTokens: 800,
          urgencyLevel: 'high',
          includeRegionalContext: true
        }
      );

      expect(simplifiedContext).toBeDefined();
      expect(simplifiedContext.contextSummary).toBeDefined();
      expect(simplifiedContext.priorityAlerts).toBeDefined();
      expect(simplifiedContext.keyRecommendations).toBeDefined();
      expect(simplifiedContext.tokenCount).toBeGreaterThan(0);

      // Verify context summary includes key patient information
      expect(simplifiedContext.contextSummary).toContain('John Doe');
      expect(simplifiedContext.contextSummary).toContain('Hypertension');
      expect(simplifiedContext.contextSummary).toContain('Ghana');

      // Verify priority alerts for chest pain
      expect(simplifiedContext.priorityAlerts.length).toBeGreaterThan(0);
      expect(simplifiedContext.priorityAlerts.some(alert =>
        alert.includes('EMERGENCY') || alert.includes('CRITICAL')
      )).toBe(true);

      // Verify token count is reasonable
      expect(simplifiedContext.tokenCount).toBeLessThan(1000);
    });

    test('should include medical history in LLM prompts', async () => {
      let capturedSystemPrompt: string = '';

      vi.spyOn(aiOrchestrator, 'generateResponse').mockImplementation(async (options) => {
        const systemMessage = options.messages.find((msg: any) => msg.role === 'system');
        if (systemMessage) {
          capturedSystemPrompt = systemMessage.content;
        }
        return {
          success: true,
          data: {
            content: 'Mocked response',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 1000
          },
          timestamp: new Date().toISOString()
        };
      });

      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'Can I take ibuprofen with my current medications?',
        userId: mockUserId,
        urgencyLevel: 'medium'
      });

      // Verify medical history is included in system prompt
      expect(capturedSystemPrompt).toContain('Hypertension');
      expect(capturedSystemPrompt).toContain('Type 2 Diabetes');
      expect(capturedSystemPrompt).toContain('Lisinopril');
      expect(capturedSystemPrompt).toContain('Metformin');

      // Verify regional context is included
      expect(capturedSystemPrompt).toContain('Ghana');
    });

    test('should handle context-aware emergency detection', async () => {
      let capturedSystemPrompt: string = '';

      vi.spyOn(aiOrchestrator, 'generateResponse').mockImplementation(async (options) => {
        const systemMessage = options.messages.find((msg: any) => msg.role === 'system');
        if (systemMessage) {
          capturedSystemPrompt = systemMessage.content;
        }
        return {
          success: true,
          data: {
            content: 'EMERGENCY: Seek immediate medical attention',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 500
          },
          timestamp: new Date().toISOString()
        };
      });

      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: mockSessionId,
        userMessage: 'I have severe chest pain and can\'t breathe',
        userId: mockUserId,
        urgencyLevel: 'critical'
      });

      // Verify emergency context is included
      expect(capturedSystemPrompt).toContain('PRIORITY ALERTS');
      expect(capturedSystemPrompt).toContain('EMERGENCY');

      // Verify patient's cardiac risk factors are considered
      expect(capturedSystemPrompt).toContain('Hypertension');
    });
  });
});

// Manual test runner for development
export async function runContextIntegrationTests() {
  console.log('🧪 Running Context Integration Tests...');
  
  try {
    // Test 1: Basic context loading
    console.log('1. Testing patient context loading...');
    const context = await enhancedPatientContextService.loadPatientContext(
      'demo-user-123', 
      'test-session'
    );
    console.log('✅ Context loaded:', {
      hasProfile: !!context.patientProfile,
      hasMedicalHistory: !!context.medicalHistory,
      hasRegionalContext: !!context.regionalContext
    });

    // Test 2: Context assembly
    console.log('2. Testing context assembly...');
    const assembled = await contextAssemblyService.assembleContext(
      context,
      [],
      'I have chest pain',
      { urgencyLevel: 'high' }
    );
    console.log('✅ Context assembled:', {
      tokenCount: assembled.tokenUsage.contextTokens,
      hasPatientProfile: assembled.contextBlock.patientProfile.length > 0,
      hasPriorityFlags: assembled.priorityFlags.length > 0
    });

    // Test 3: Agent orchestrator integration
    console.log('3. Testing agent orchestrator with context...');
    const response = await agentOrchestrator.processRequest({
      sessionId: 'test-session',
      userMessage: 'I have chest pain and shortness of breath',
      userId: 'demo-user-123',
      urgencyLevel: 'high'
    });
    console.log('✅ Agent response with context:', {
      hasResponse: !!response.agentResponse.content,
      hasEmergencyFlags: response.agentResponse.emergencyFlags?.length > 0,
      responseLength: response.agentResponse.content.length
    });

    console.log('🎉 All context integration tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Context integration test failed:', error);
    return false;
  }
}
