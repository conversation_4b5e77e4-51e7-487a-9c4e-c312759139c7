import { supabase } from './supabaseClient';
import audioStorageService from './audioStorageService';
import sendPulseService from './sendPulseService';

/**
 * Async Messaging Service
 * Handles asynchronous voice messages, notifications, and message queues
 */
class AsyncMessagingService {
  constructor() {
    this.messageQueue = new Map();
    this.listeners = new Map();
    this.retryAttempts = 3;
    this.retryDelay = 5000; // 5 seconds
    this.notificationQueue = [];
    this.isProcessingQueue = false;
  }

  /**
   * Send async voice message
   */
  async sendVoiceMessage(messageData) {
    try {
      const messageId = `async_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Store audio message
      const storageResult = await audioStorageService.storeAudioMessage(
        messageData.audioBlob,
        {
          ...messageData,
          messageType: 'async_voice',
          messageId
        }
      );

      if (!storageResult.success) {
        throw new Error('Failed to store audio message');
      }

      // Create message queue entry
      const queueEntry = {
        id: messageId,
        type: 'voice_message',
        senderId: messageData.senderId,
        recipientId: messageData.recipientId,
        sessionId: messageData.sessionId,
        priority: messageData.priority || 'normal',
        status: 'pending',
        attempts: 0,
        audioStorageId: storageResult.messageId,
        transcription: messageData.transcription,
        duration: messageData.duration,
        timestamp: new Date().toISOString(),
        metadata: {
          quality: messageData.quality || 'medium',
          urgent: messageData.urgent || false,
          requiresResponse: messageData.requiresResponse || false
        }
      };

      // Add to message queue
      this.messageQueue.set(messageId, queueEntry);

      // Store in database
      await this.storeMessageInDB(queueEntry);

      // Send notification to recipient
      await this.sendMessageNotification(queueEntry);

      // Process message queue
      this.processMessageQueue();

      return {
        success: true,
        messageId,
        status: 'queued',
        estimatedDelivery: this.calculateDeliveryTime(queueEntry.priority)
      };
    } catch (error) {
      console.error('Failed to send voice message:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Store message in database
   */
  async storeMessageInDB(messageData) {
    try {
      const { error } = await supabase
        .from('async_messages')
        .insert({
          id: messageData.id,
          type: messageData.type,
          sender_id: messageData.senderId,
          recipient_id: messageData.recipientId,
          session_id: messageData.sessionId,
          priority: messageData.priority,
          status: messageData.status,
          audio_storage_id: messageData.audioStorageId,
          transcription: messageData.transcription,
          duration: messageData.duration,
          metadata: messageData.metadata,
          last_activity: messageData.timestamp
        });

      if (error) {
        console.error('Failed to store message in DB:', error);
        throw error;
      }
    } catch (error) {
      console.error('Database storage error:', error);
      throw error;
    }
  }

  /**
   * Send message notification
   */
  async sendMessageNotification(messageData) {
    try {
      // Get recipient details
      const { data: recipient, error } = await supabase
        .from('user_profiles')
        .select('email, full_name, notification_preferences')
        .eq('id', messageData.recipientId)
        .single();

      if (error || !recipient) {
        console.warn('Recipient not found for notification');
        return;
      }

      // Check notification preferences
      const preferences = recipient.notification_preferences || {};
      const shouldNotify = preferences.async_messages !== false;

      if (!shouldNotify) {
        return; // User has disabled async message notifications
      }

      // Get sender details
      const { data: sender } = await supabase
        .from('user_profiles')
        .select('full_name')
        .eq('id', messageData.senderId)
        .single();

      const senderName = sender?.full_name || 'VoiceHealth AI User';

      // Send email notification
      const emailResult = await sendPulseService.sendNotification(
        recipient.email,
        recipient.full_name || recipient.email.split('@')[0],
        'New Voice Message Received',
        `You have received a new voice message from ${senderName}. Duration: ${this.formatDuration(messageData.duration)}. ${messageData.transcription ? `Preview: "${messageData.transcription.substring(0, 100)}..."` : ''}`,
        `${window.location.origin}/voice-consultation-interface?message=${messageData.id}`,
        'Listen to Message'
      );

      // Store notification record
      await this.storeNotificationRecord({
        messageId: messageData.id,
        recipientId: messageData.recipientId,
        type: 'async_message',
        channel: 'email',
        status: emailResult.success ? 'sent' : 'failed',
        timestamp: new Date().toISOString()
      });

      // Send real-time notification if user is online
      await this.sendRealTimeNotification(messageData);

    } catch (error) {
      console.error('Failed to send message notification:', error);
    }
  }

  /**
   * Send real-time notification via WebSocket
   */
  async sendRealTimeNotification(messageData) {
    try {
      // Check if recipient has active WebSocket connection
      const notification = {
        type: 'async_voice_message',
        messageId: messageData.id,
        senderId: messageData.senderId,
        duration: messageData.duration,
        transcription: messageData.transcription,
        priority: messageData.priority,
        timestamp: messageData.timestamp,
        metadata: messageData.metadata
      };

      // In a real implementation, this would send via WebSocket server
      // For now, we'll use browser notifications if permission granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('New Voice Message', {
          body: `${this.formatDuration(messageData.duration)} voice message received`,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          tag: `voice-message-${messageData.id}`,
          data: { messageId: messageData.id },
          requireInteraction: messageData.metadata?.urgent
        });
      }

      // Store in browser storage for retrieval
      const notifications = JSON.parse(localStorage.getItem('voicehealth_notifications') || '[]');
      notifications.unshift(notification);
      
      // Keep only latest 50 notifications
      if (notifications.length > 50) {
        notifications.splice(50);
      }
      
      localStorage.setItem('voicehealth_notifications', JSON.stringify(notifications));

      // Dispatch custom event for real-time UI updates
      window.dispatchEvent(new CustomEvent('voiceMessageReceived', {
        detail: notification
      }));

    } catch (error) {
      console.error('Real-time notification failed:', error);
    }
  }

  /**
   * Store notification record
   */
  async storeNotificationRecord(notificationData) {
    try {
      const { error } = await supabase
        .from('message_notifications')
        .insert({
          message_id: notificationData.messageId,
          recipient_id: notificationData.recipientId,
          type: notificationData.type,
          channel: notificationData.channel,
          status: notificationData.status,
          last_activity: notificationData.timestamp
        });

      if (error) {
        console.error('Failed to store notification record:', error);
      }
    } catch (error) {
      console.error('Notification storage error:', error);
    }
  }

  /**
   * Get user's async messages
   */
  async getUserMessages(userId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        status = null,
        sessionId = null,
        includeAudio = false
      } = options;

      let query = supabase
        .from('async_messages')
        .select(`
          *,
          sender:user_profiles!async_messages_sender_id_fkey(full_name, avatar_url),
          recipient:user_profiles!async_messages_recipient_id_fkey(full_name, avatar_url)
        `)
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      if (sessionId) {
        query = query.eq('session_id', sessionId);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      // Load audio data if requested
      if (includeAudio) {
        for (const message of data) {
          if (message.audio_storage_id) {
            const audioResult = await audioStorageService.getAudioMessage(message.audio_storage_id);
            if (audioResult.success) {
              message.audioData = audioResult.data;
            }
          }
        }
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get specific message with audio
   */
  async getMessage(messageId, userId) {
    try {
      const { data, error } = await supabase
        .from('async_messages')
        .select(`
          *,
          sender:user_profiles!async_messages_sender_id_fkey(full_name, avatar_url),
          recipient:user_profiles!async_messages_recipient_id_fkey(full_name, avatar_url)
        `)
        .eq('id', messageId)
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Load audio data
      if (data.audio_storage_id) {
        const audioResult = await audioStorageService.getAudioMessage(data.audio_storage_id);
        if (audioResult.success) {
          data.audioData = audioResult.data;
        }
      }

      // Mark as read if user is recipient
      if (data.recipient_id === userId && data.status === 'delivered') {
        await this.markMessageAsRead(messageId);
        data.status = 'read';
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId) {
    try {
      const { error } = await supabase
        .from('async_messages')
        .update({ 
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        console.error('Failed to mark message as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Mark as read error:', error);
      return false;
    }
  }

  /**
   * Process message queue
   */
  async processMessageQueue() {
    if (this.isProcessingQueue) return;
    this.isProcessingQueue = true;

    try {
      const pendingMessages = Array.from(this.messageQueue.values())
        .filter(msg => msg.status === 'pending')
        .sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));

      for (const message of pendingMessages) {
        try {
          await this.deliverMessage(message);
        } catch (error) {
          console.error(`Failed to deliver message ${message.id}:`, error);
          await this.handleDeliveryFailure(message, error);
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Deliver message
   */
  async deliverMessage(message) {
    try {
      // Update message status to delivering
      message.status = 'delivering';
      message.attempts += 1;
      await this.updateMessageStatus(message.id, 'delivering');

      // Simulate delivery process (in real implementation, this would involve WebSocket delivery)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update to delivered
      message.status = 'delivered';
      await this.updateMessageStatus(message.id, 'delivered');

      // Remove from queue
      this.messageQueue.delete(message.id);

      console.log(`Message ${message.id} delivered successfully`);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle delivery failure
   */
  async handleDeliveryFailure(message, error) {
    try {
      if (message.attempts < this.retryAttempts) {
        // Schedule retry
        message.status = 'pending';
        await this.updateMessageStatus(message.id, 'pending');
        
        setTimeout(() => {
          this.processMessageQueue();
        }, this.retryDelay * message.attempts);
        
        console.log(`Message ${message.id} scheduled for retry (attempt ${message.attempts + 1})`);
      } else {
        // Mark as failed
        message.status = 'failed';
        await this.updateMessageStatus(message.id, 'failed');
        this.messageQueue.delete(message.id);
        
        console.error(`Message ${message.id} delivery failed permanently:`, error);
        
        // Notify sender of failure
        await this.notifyDeliveryFailure(message);
      }
    } catch (updateError) {
      console.error('Failed to handle delivery failure:', updateError);
    }
  }

  /**
   * Update message status in database
   */
  async updateMessageStatus(messageId, status) {
    try {
      const { error } = await supabase
        .from('async_messages')
        .update({ 
          status,
          last_activity: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        console.error('Failed to update message status:', error);
      }
    } catch (error) {
      console.error('Status update error:', error);
    }
  }

  /**
   * Notify delivery failure
   */
  async notifyDeliveryFailure(message) {
    try {
      // Get sender details
      const { data: sender } = await supabase
        .from('user_profiles')
        .select('email, full_name')
        .eq('id', message.senderId)
        .single();

      if (sender?.email) {
        await sendPulseService.sendNotification(
          sender.email,
          sender.full_name || sender.email.split('@')[0],
          'Voice Message Delivery Failed',
          `Your voice message could not be delivered. The recipient may be offline or have connectivity issues. You can try sending the message again later.`,
          `${window.location.origin}/voice-consultation-interface`,
          'Try Again'
        );
      }
    } catch (error) {
      console.error('Failed to notify delivery failure:', error);
    }
  }

  /**
   * Get priority weight for sorting
   */
  getPriorityWeight(priority) {
    const weights = {
      urgent: 100,
      high: 75,
      normal: 50,
      low: 25
    };
    return weights[priority] || weights.normal;
  }

  /**
   * Calculate estimated delivery time
   */
  calculateDeliveryTime(priority) {
    const queueSize = this.messageQueue.size;
    const priorityMultiplier = {
      urgent: 0.1,
      high: 0.5,
      normal: 1,
      low: 2
    };
    
    const baseTime = 5000; // 5 seconds base time
    const queueDelay = queueSize * 1000; // 1 second per queued message
    const priorityDelay = baseTime * (priorityMultiplier[priority] || 1);
    
    return new Date(Date.now() + queueDelay + priorityDelay);
  }

  /**
   * Format duration helper
   */
  formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Get message queue status
   */
  getQueueStatus() {
    const messages = Array.from(this.messageQueue.values());
    return {
      total: messages.length,
      pending: messages.filter(m => m.status === 'pending').length,
      delivering: messages.filter(m => m.status === 'delivering').length,
      failed: messages.filter(m => m.status === 'failed').length,
      isProcessing: this.isProcessingQueue
    };
  }

  /**
   * Clear message queue
   */
  clearQueue() {
    this.messageQueue.clear();
    this.isProcessingQueue = false;
  }

  /**
   * Add message listener
   */
  addMessageListener(userId, callback) {
    if (!this.listeners.has(userId)) {
      this.listeners.set(userId, new Set());
    }
    this.listeners.get(userId).add(callback);
    
    return () => {
      const userListeners = this.listeners.get(userId);
      if (userListeners) {
        userListeners.delete(callback);
        if (userListeners.size === 0) {
          this.listeners.delete(userId);
        }
      }
    };
  }

  /**
   * Notify listeners of new message
   */
  notifyListeners(userId, message) {
    const userListeners = this.listeners.get(userId);
    if (userListeners) {
      userListeners.forEach(callback => {
        try {
          callback(message);
        } catch (error) {
          console.error('Listener callback error:', error);
        }
      });
    }
  }

  /**
   * Get unread message count
   */
  async getUnreadCount(userId) {
    try {
      const { count, error } = await supabase
        .from('async_messages')
        .select('*', { count: 'exact', head: true })
        .eq('recipient_id', userId)
        .in('status', ['delivered', 'pending']);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, count };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete message
   */
  async deleteMessage(messageId, userId) {
    try {
      // Verify user owns the message
      const { data: message } = await supabase
        .from('async_messages')
        .select('sender_id, recipient_id, audio_storage_id')
        .eq('id', messageId)
        .single();

      if (!message || (message.sender_id !== userId && message.recipient_id !== userId)) {
        return { success: false, error: 'Message not found or access denied' };
      }

      // Delete audio file
      if (message.audio_storage_id) {
        await audioStorageService.deleteAudioMessage(message.audio_storage_id);
      }

      // Delete message record
      const { error } = await supabase
        .from('async_messages')
        .delete()
        .eq('id', messageId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Remove from queue if pending
      this.messageQueue.delete(messageId);

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

const asyncMessagingService = new AsyncMessagingService();
export default asyncMessagingService;