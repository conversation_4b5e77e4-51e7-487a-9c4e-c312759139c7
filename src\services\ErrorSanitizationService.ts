/**
 * ERROR SANITIZATION SERVICE
 * 
 * HIPAA-compliant error message sanitization and structured error handling
 * to prevent sensitive medical data exposure in error messages and logs.
 * 
 * FEATURES:
 * - HIPAA-compliant error message sanitization
 * - Structured error logging with audit trails
 * - User-friendly error messages without sensitive data
 * - Error categorization (technical, user, security)
 * - Emergency error handling with bypass mechanisms
 * - Comprehensive error tracking and analytics
 */

import auditLogger from '../utils/auditLogger';

export interface SanitizedError {
  id: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  userMessage: string;
  technicalMessage: string;
  timestamp: string;
  context?: ErrorContext;
  emergencyBypass?: boolean;
}

export interface ErrorContext {
  sessionId?: string;
  userId?: string;
  agentId?: string;
  operation?: string;
  component?: string;
  requestId?: string;
}

export type ErrorCategory = 'technical' | 'user' | 'security' | 'medical' | 'network' | 'validation';
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical' | 'emergency';

export interface ErrorPattern {
  pattern: RegExp;
  category: ErrorCategory;
  severity: ErrorSeverity;
  userMessage: string;
  sanitizationRules: string[];
}

export class ErrorSanitizationService {
  private errorPatterns: ErrorPattern[] = [];
  private sensitiveDataPatterns: RegExp[] = [];
  private errorCounts: Map<string, number> = new Map();
  private emergencyErrorThreshold = 10;

  constructor() {
    console.log('🛡️ Initializing Error Sanitization Service...');
    this.initializeErrorPatterns();
    this.initializeSensitiveDataPatterns();
    this.startErrorMonitoring();
  }

  /**
   * Initialize error patterns for categorization and sanitization
   */
  private initializeErrorPatterns(): void {
    this.errorPatterns = [
      // Database errors
      {
        pattern: /database|sql|postgres|supabase/i,
        category: 'technical',
        severity: 'high',
        userMessage: 'A temporary system issue occurred. Please try again.',
        sanitizationRules: ['remove_connection_strings', 'remove_table_names', 'remove_query_details']
      },

      // Authentication errors
      {
        pattern: /auth|token|unauthorized|forbidden/i,
        category: 'security',
        severity: 'medium',
        userMessage: 'Authentication required. Please log in again.',
        sanitizationRules: ['remove_tokens', 'remove_user_ids', 'remove_session_details']
      },

      // Medical data errors
      {
        pattern: /medical|patient|diagnosis|symptom|medication/i,
        category: 'medical',
        severity: 'critical',
        userMessage: 'Unable to process medical information. Please contact support.',
        sanitizationRules: ['remove_medical_data', 'remove_patient_info', 'remove_diagnosis_details']
      },

      // Network errors
      {
        pattern: /network|timeout|connection|fetch|request/i,
        category: 'network',
        severity: 'medium',
        userMessage: 'Connection issue detected. Please check your internet connection.',
        sanitizationRules: ['remove_urls', 'remove_ip_addresses', 'remove_api_keys']
      },

      // Validation errors
      {
        pattern: /validation|invalid|required|format/i,
        category: 'validation',
        severity: 'low',
        userMessage: 'Please check your input and try again.',
        sanitizationRules: ['remove_field_names', 'remove_input_values']
      },

      // Emergency errors
      {
        pattern: /emergency|critical|urgent|life.threatening/i,
        category: 'medical',
        severity: 'emergency',
        userMessage: 'Emergency situation detected. Immediate medical attention may be required.',
        sanitizationRules: ['preserve_emergency_context', 'remove_personal_details']
      }
    ];

    console.log(`✅ Initialized ${this.errorPatterns.length} error patterns`);
  }

  /**
   * Initialize patterns for detecting sensitive data
   */
  private initializeSensitiveDataPatterns(): void {
    this.sensitiveDataPatterns = [
      // Personal identifiers
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
      /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, // Phone numbers
      
      // Medical identifiers
      /\bMRN[-\s]?\d+\b/gi, // Medical record numbers
      /\bpatient[-\s]?id[-\s]?\d+\b/gi, // Patient IDs
      /\bdob[-\s]?\d{1,2}\/\d{1,2}\/\d{4}\b/gi, // Date of birth
      
      // System identifiers
      /\bapi[-_]?key[-_]?[a-zA-Z0-9]{20,}\b/gi, // API keys
      /\btoken[-_]?[a-zA-Z0-9]{20,}\b/gi, // Tokens
      /\bpassword[-_]?[a-zA-Z0-9]{8,}\b/gi, // Passwords
      
      // Database details
      /\bconnection[-_]?string\b/gi,
      /\btable[-_]?name\b/gi,
      /\bquery[-_]?text\b/gi
    ];

    console.log(`✅ Initialized ${this.sensitiveDataPatterns.length} sensitive data patterns`);
  }

  /**
   * Sanitize error message for HIPAA compliance
   */
  public sanitizeError(
    error: Error | string, 
    context?: ErrorContext,
    emergencyBypass?: boolean
  ): SanitizedError {
    const errorId = this.generateErrorId();
    const errorMessage = error instanceof Error ? error.message : error;
    const stack = error instanceof Error ? error.stack : undefined;

    try {
      // Categorize the error
      const pattern = this.categorizeError(errorMessage);
      
      // Apply sanitization rules
      const sanitizedMessage = this.applySanitizationRules(errorMessage, pattern.sanitizationRules);
      
      // Generate user-friendly message
      const userMessage = emergencyBypass && pattern.severity === 'emergency' 
        ? this.generateEmergencyMessage(errorMessage, context)
        : pattern.userMessage;

      // Create sanitized error object
      const sanitizedError: SanitizedError = {
        id: errorId,
        category: pattern.category,
        severity: pattern.severity,
        userMessage,
        technicalMessage: sanitizedMessage,
        timestamp: new Date().toISOString(),
        context,
        emergencyBypass
      };

      // Log the error for audit trail
      this.logSanitizedError(sanitizedError, stack);

      // Track error frequency
      this.trackErrorFrequency(pattern.category, pattern.severity);

      // Check for emergency conditions
      if (pattern.severity === 'emergency' || this.isEmergencyCondition(pattern.category)) {
        this.handleEmergencyError(sanitizedError);
      }

      return sanitizedError;

    } catch (sanitizationError) {
      console.error('❌ Error during sanitization:', sanitizationError);
      
      // Fallback sanitized error
      return {
        id: errorId,
        category: 'technical',
        severity: 'high',
        userMessage: 'An unexpected error occurred. Please contact support.',
        technicalMessage: 'Error sanitization failed',
        timestamp: new Date().toISOString(),
        context,
        emergencyBypass
      };
    }
  }

  /**
   * Categorize error based on patterns
   */
  private categorizeError(errorMessage: string): ErrorPattern {
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(errorMessage)) {
        return pattern;
      }
    }

    // Default pattern for uncategorized errors
    return {
      pattern: /.*/,
      category: 'technical',
      severity: 'medium',
      userMessage: 'A system error occurred. Please try again.',
      sanitizationRules: ['remove_sensitive_data']
    };
  }

  /**
   * Apply sanitization rules to error message
   */
  private applySanitizationRules(message: string, rules: string[]): string {
    let sanitized = message;

    for (const rule of rules) {
      switch (rule) {
        case 'remove_sensitive_data':
          sanitized = this.removeSensitiveData(sanitized);
          break;
        case 'remove_medical_data':
          sanitized = this.removeMedicalData(sanitized);
          break;
        case 'remove_patient_info':
          sanitized = this.removePatientInfo(sanitized);
          break;
        case 'remove_tokens':
          sanitized = this.removeTokens(sanitized);
          break;
        case 'remove_connection_strings':
          sanitized = this.removeConnectionStrings(sanitized);
          break;
        case 'remove_urls':
          sanitized = this.removeUrls(sanitized);
          break;
        case 'preserve_emergency_context':
          // Special handling for emergency errors
          break;
        default:
          console.warn(`Unknown sanitization rule: ${rule}`);
      }
    }

    return sanitized;
  }

  /**
   * Remove sensitive data using patterns
   */
  private removeSensitiveData(message: string): string {
    let sanitized = message;
    
    for (const pattern of this.sensitiveDataPatterns) {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    }
    
    return sanitized;
  }

  /**
   * Remove medical data from error messages
   */
  private removeMedicalData(message: string): string {
    return message
      .replace(/\b(diagnosis|symptom|medication|treatment|condition):\s*[^,\n]*/gi, '$1: [REDACTED]')
      .replace(/\b(patient|mrn|medical record)[-\s]*\w+/gi, '[PATIENT_ID_REDACTED]')
      .replace(/\b(blood pressure|heart rate|temperature):\s*[\d\/]+/gi, '$1: [REDACTED]');
  }

  /**
   * Remove patient information
   */
  private removePatientInfo(message: string): string {
    return message
      .replace(/\b(name|first name|last name):\s*\w+/gi, '$1: [REDACTED]')
      .replace(/\b(age|dob|date of birth):\s*[\d\/\-]+/gi, '$1: [REDACTED]')
      .replace(/\b(address|phone|email):\s*[^,\n]*/gi, '$1: [REDACTED]');
  }

  /**
   * Remove authentication tokens
   */
  private removeTokens(message: string): string {
    return message
      .replace(/\b(token|jwt|bearer)[-\s]*[a-zA-Z0-9._-]{20,}/gi, '$1: [REDACTED]')
      .replace(/\b(api[-_]?key)[-\s]*[a-zA-Z0-9]{20,}/gi, '$1: [REDACTED]');
  }

  /**
   * Remove database connection strings
   */
  private removeConnectionStrings(message: string): string {
    return message
      .replace(/postgresql:\/\/[^,\s]*/gi, 'postgresql://[REDACTED]')
      .replace(/\b(host|server|database)=\w+/gi, '$1=[REDACTED]')
      .replace(/\b(password|pwd)=\w+/gi, '$1=[REDACTED]');
  }

  /**
   * Remove URLs and IP addresses
   */
  private removeUrls(message: string): string {
    return message
      .replace(/https?:\/\/[^\s,]*/gi, '[URL_REDACTED]')
      .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP_REDACTED]');
  }

  /**
   * Generate emergency-specific message
   */
  private generateEmergencyMessage(errorMessage: string, context?: ErrorContext): string {
    const baseMessage = 'Emergency situation detected.';
    
    if (context?.sessionId) {
      return `${baseMessage} Session: ${context.sessionId.substring(0, 8)}... Please contact emergency services if needed.`;
    }
    
    return `${baseMessage} Please contact emergency services if immediate medical attention is required.`;
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log sanitized error for audit trail
   */
  private async logSanitizedError(sanitizedError: SanitizedError, stack?: string): Promise<void> {
    try {
      await auditLogger.logError({
        errorId: sanitizedError.id,
        category: sanitizedError.category,
        severity: sanitizedError.severity,
        sanitizedMessage: sanitizedError.technicalMessage,
        userMessage: sanitizedError.userMessage,
        context: sanitizedError.context,
        stack: stack ? this.sanitizeStackTrace(stack) : undefined,
        timestamp: sanitizedError.timestamp
      });
    } catch (loggingError) {
      console.error('❌ Failed to log sanitized error:', loggingError);
    }
  }

  /**
   * Sanitize stack trace
   */
  private sanitizeStackTrace(stack: string): string {
    return this.removeSensitiveData(stack)
      .replace(/\/[^\/\s]*\/[^\/\s]*\/[^\/\s]*/g, '/[PATH_REDACTED]')
      .replace(/at\s+[^(]*\([^)]*\)/g, 'at [FUNCTION_REDACTED]');
  }

  /**
   * Track error frequency for monitoring
   */
  private trackErrorFrequency(category: ErrorCategory, severity: ErrorSeverity): void {
    const key = `${category}_${severity}`;
    const count = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, count + 1);

    // Log high frequency errors
    if (count > 5) {
      console.warn(`⚠️ High frequency error detected: ${key} (${count + 1} occurrences)`);
    }
  }

  /**
   * Check if emergency condition exists
   */
  private isEmergencyCondition(category: ErrorCategory): boolean {
    const categoryCount = Array.from(this.errorCounts.entries())
      .filter(([key]) => key.startsWith(category))
      .reduce((sum, [, count]) => sum + count, 0);

    return categoryCount >= this.emergencyErrorThreshold;
  }

  /**
   * Handle emergency errors
   */
  private handleEmergencyError(sanitizedError: SanitizedError): void {
    console.error(`🚨 Emergency error detected: ${sanitizedError.id}`);
    
    // Could trigger additional emergency protocols here
    // such as alerting administrators, escalating to emergency services, etc.
  }

  /**
   * Start error monitoring
   */
  private startErrorMonitoring(): void {
    // Reset error counts every hour
    setInterval(() => {
      this.errorCounts.clear();
      console.log('🔄 Error frequency counters reset');
    }, 60 * 60 * 1000);

    console.log('📊 Error monitoring started');
  }

  /**
   * Get error statistics
   */
  public getErrorStatistics(): Record<string, number> {
    return Object.fromEntries(this.errorCounts);
  }

  /**
   * Reset error tracking
   */
  public resetErrorTracking(): void {
    this.errorCounts.clear();
    console.log('🔄 Error tracking reset');
  }
}

// Export singleton instance
export const errorSanitizationService = new ErrorSanitizationService();
export default errorSanitizationService;
