/**
 * SECURE AUTHENTICATION API
 * Handles httpOnly cookie-based token storage for enhanced security
 */

const express = require('express');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');
const csrf = require('csrf');
const router = express.Router();

// Initialize CSRF protection
const tokens = new csrf();
const csrfSecret = process.env.CSRF_SECRET || 'your-secret-csrf-key-change-in-production';

const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 8 * 60 * 60 * 1000, // 8 hours
  path: '/'
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
};

// Allowed origins for CORS (frontend applications)
const ALLOWED_ORIGINS = [
  'http://localhost:4028', // Development
  'https://voicehealth-ai.vercel.app', // Production
  'https://voicehealth-ai-staging.vercel.app', // Staging
  process.env.FRONTEND_URL || 'http://localhost:4028'
].filter(Boolean);

/**
 * CSRF protection middleware
 */
function csrfProtection(req, res, next) {
  try {
    // Get CSRF token from header or body
    const token = req.headers['x-csrf-token'] || req.body._csrf;
    
    if (!token) {
      return res.status(403).json({
        success: false,
        error: 'CSRF token required'
      });
    }

    // Get CSRF secret from cookie
    const secret = req.cookies['csrf-secret'];
    
    if (!secret) {
      return res.status(403).json({
        success: false,
        error: 'CSRF secret not found'
      });
    }

    // Verify CSRF token
    if (!tokens.verify(secret, token)) {
      return res.status(403).json({
        success: false,
        error: 'Invalid CSRF token'
      });
    }

    next();
  } catch (error) {
    console.error('CSRF validation error:', error);
    res.status(500).json({
      success: false,
      error: 'CSRF validation failed'
    });
  }
}

/**
 * Generate and set CSRF token
 */
router.get('/csrf-token', (req, res) => {
  try {
    const secret = tokens.secretSync();
    const token = tokens.create(secret);
    
    // Set CSRF secret in httpOnly cookie
    res.cookie('csrf-secret', secret, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 8 * 60 * 60 * 1000 // 8 hours
    });
    
    res.json({
      success: true,
      csrfToken: token
    });
  } catch (error) {
    console.error('Error generating CSRF token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate CSRF token'
    });
  }
});

/**
 * Middleware to validate request origin and authenticate user
 */
function authenticateRequest(req, res, next) {
  try {
    // Check origin for non-GET requests
    if (req.method !== 'GET') {
      const origin = req.headers.origin || req.headers.referer;
      const isValidOrigin = ALLOWED_ORIGINS.some(allowedOrigin => {
        if (!origin) return false;
        return origin.startsWith(allowedOrigin);
      });

      if (!isValidOrigin && process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          success: false,
          error: 'Request not authorized from this origin'
        });
      }
    }

    // For auth token operations, verify that a valid session token exists
    // This prevents unauthorized access to token management endpoints
    const sessionToken = req.cookies['voicehealth_auth_token'];
    
    // Allow initial token storage (when no session exists yet)
    if (req.path === '/store-token' && !sessionToken) {
      return next();
    }

    // For other operations, require existing valid session
    if (!sessionToken) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Validate token structure
    if (!isValidJWTStructure(sessionToken)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid authentication token'
      });
    }

    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication validation failed'
    });
  }
}


/**
 * Validates JWT token structure without verifying signature
 * @param {string} token - JWT token to validate
 * @returns {boolean} - True if token has valid JWT structure
 */
function isValidJWTStructure(token) {
  if (!token || typeof token !== 'string') {
    return false;
  }

  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  if (parts.length !== 3) {
    return false;
  }

  try {
    // Validate that header and payload are valid base64url encoded JSON
    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    // Basic JWT header validation
    if (!header.alg || !header.typ || header.typ !== 'JWT') {
      return false;
    }

    // Basic payload validation
    if (typeof payload !== 'object' || payload === null) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Sanitizes metadata to prevent XSS and other security issues
 * @param {object} metadata - Metadata object to sanitize
 * @returns {object} - Sanitized metadata
 */
function sanitizeMetadata(metadata) {
  if (!metadata || typeof metadata !== 'object') {
    return {};
  }

  const sanitized = {};
  
  for (const [key, value] of Object.entries(metadata)) {
    // Only allow alphanumeric keys with underscores and hyphens
    if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
      continue;
    }

    // Sanitize string values
    if (typeof value === 'string') {
      // Remove potential XSS vectors and limit length
      sanitized[key] = value
        .replace(/[<>\"'&]/g, '') // Remove HTML/XML special characters
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .substring(0, 500); // Limit length
    } else if (typeof value === 'number' && isFinite(value)) {
      sanitized[key] = value;
    } else if (typeof value === 'boolean') {
      sanitized[key] = value;
    }
    // Ignore other types (objects, arrays, functions, etc.)
  }

  return sanitized;
}

/**
 * Store authentication token in httpOnly cookie
 */
router.post('/store-token', csrfProtection, authenticateRequest, (req, res) => {
  try {
    const { token, tokenType = 'access', metadata = {} } = req.body;

    // Validate token presence
    if (!token) {
      return res.status(400).json({
        success: false,
        error: 'Token is required'
      });
    }

    // Validate token type
    if (!['access', 'refresh'].includes(tokenType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid token type. Must be "access" or "refresh"'
      });
    }

    // Validate JWT structure
    if (!isValidJWTStructure(token)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid token format. Token must be a valid JWT'
      });
    }

    // Sanitize metadata
    const sanitizedMetadata = sanitizeMetadata(metadata);

    // Validate metadata size to prevent DoS
    const metadataString = JSON.stringify(sanitizedMetadata);
    if (metadataString.length > 2048) { // 2KB limit
      return res.status(400).json({
        success: false,
        error: 'Metadata too large. Maximum size is 2KB'
      });
    }

    const cookieName = tokenType === 'refresh' ? 'voicehealth_refresh_token' : 'voicehealth_auth_token';
    const options = tokenType === 'refresh' ? REFRESH_COOKIE_OPTIONS : COOKIE_OPTIONS;

    // Set the httpOnly cookie
    res.cookie(cookieName, token, options);

    // Store sanitized metadata in a separate httpOnly cookie
    if (Object.keys(sanitizedMetadata).length > 0) {
      const finalMetadata = {
        ...sanitizedMetadata,
        tokenType,
        storedAt: Date.now()
      };
      
      res.cookie(`${cookieName}_metadata`, JSON.stringify(finalMetadata), {
        ...options,
        maxAge: options.maxAge
      });
    }

    res.json({
      success: true,
      message: `${tokenType} token stored securely`,
      tokenType,
      metadataKeys: Object.keys(sanitizedMetadata)
    });

  } catch (error) {
    console.error('Error storing token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to store token'
    });
  }
});

/**
 * Retrieve authentication token from httpOnly cookie
 */
router.get('/get-token', authenticateRequest, (req, res) => {
  try {
    const { tokenType = 'access' } = req.query;
    const cookieName = tokenType === 'refresh' ? 'voicehealth_refresh_token' : 'voicehealth_auth_token';
    
    const token = req.cookies[cookieName];
    const metadataString = req.cookies[`${cookieName}_metadata`];
    
    if (!token) {
      return res.json({
        success: true,
        token: null,
        metadata: null
      });
    }

    let metadata = null;
    if (metadataString) {
      try {
        metadata = JSON.parse(metadataString);
        
        // Check if token has expired
        if (metadata.expiresAt && Date.now() > metadata.expiresAt) {
          // Clear expired cookies
          res.clearCookie(cookieName, { path: '/' });
          res.clearCookie(`${cookieName}_metadata`, { path: '/' });
          
          return res.json({
            success: true,
            token: null,
            metadata: null,
            expired: true
          });
        }
      } catch (parseError) {
        console.error('Error parsing token metadata:', parseError);
      }
    }

    res.json({
      success: true,
      token,
      metadata
    });

  } catch (error) {
    console.error('Error retrieving token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve token'
    });
  }
});

/**
 * Clear all authentication cookies
 */
router.post('/clear-tokens', csrfProtection, authenticateRequest, (req, res) => {
  try {
    const cookieNames = [
      'voicehealth_auth_token',
      'voicehealth_refresh_token',
      'voicehealth_auth_token_metadata',
      'voicehealth_refresh_token_metadata'
    ];

    cookieNames.forEach(cookieName => {
      res.clearCookie(cookieName, { path: '/' });
    });

    res.json({
      success: true,
      message: 'All authentication tokens cleared'
    });

  } catch (error) {
    console.error('Error clearing tokens:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear tokens'
    });
  }
});

/**
 * Check if user has valid authentication
 */
router.get('/validate-token', authenticateRequest, (req, res) => {
  try {
    const { tokenType = 'access' } = req.query;
    const cookieName = tokenType === 'refresh' ? 'voicehealth_refresh_token' : 'voicehealth_auth_token';
    
    const token = req.cookies[cookieName];
    const metadataString = req.cookies[`${cookieName}_metadata`];
    
    if (!token) {
      return res.json({
        success: true,
        valid: false,
        reason: 'No token found'
      });
    }

    if (metadataString) {
      try {
        const metadata = JSON.parse(metadataString);
        
        if (metadata.expiresAt && Date.now() > metadata.expiresAt) {
          // Clear expired cookies
          res.clearCookie(cookieName, { path: '/' });
          res.clearCookie(`${cookieName}_metadata`, { path: '/' });
          
          return res.json({
            success: true,
            valid: false,
            reason: 'Token expired'
          });
        }
      } catch (parseError) {
        console.error('Error parsing token metadata:', parseError);
      }
    }

    res.json({
      success: true,
      valid: true
    });

  } catch (error) {
    console.error('Error validating token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate token'
    });
  }
});

/**
 * Rotate token on suspicious activity
 */
router.post('/rotate-token', csrfProtection, authenticateRequest, (req, res) => {
  try {
    const { newToken, metadata = {} } = req.body;

    if (!newToken) {
      return res.status(400).json({
        success: false,
        error: 'New token is required'
      });
    }

    // Clear old tokens
    const cookieNames = [
      'voicehealth_auth_token',
      'voicehealth_refresh_token',
      'voicehealth_auth_token_metadata',
      'voicehealth_refresh_token_metadata'
    ];

    cookieNames.forEach(cookieName => {
      res.clearCookie(cookieName, { path: '/' });
    });

    // Store new token with updated metadata
    res.cookie('voicehealth_auth_token', newToken, COOKIE_OPTIONS);
    
    const updatedMetadata = {
      ...metadata,
      issuedAt: Date.now(),
      rotatedDueToSuspiciousActivity: true
    };

    // Validate metadata size to prevent DoS
    const updatedMetadataString = JSON.stringify(updatedMetadata);
    if (updatedMetadataString.length > 2048) { // 2KB limit
      return res.status(400).json({
        success: false,
        error: 'Metadata too large. Maximum size is 2KB'
      });
    }

    res.cookie('voicehealth_auth_token_metadata', updatedMetadataString, COOKIE_OPTIONS);

    res.json({
      success: true,
      message: 'Token rotated successfully due to suspicious activity'
    });

  } catch (error) {
    console.error('Error rotating token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to rotate token'
    });
  }
});

module.exports = router;