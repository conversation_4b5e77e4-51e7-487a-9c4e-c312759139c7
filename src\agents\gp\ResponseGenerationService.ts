/**
 * GP RESPONSE GENERATION SERVICE
 * 
 * Handles response generation, context assembly, and AI orchestration
 * for the General Practitioner Agent.
 */

import { aiOrchestrator } from '../../services/aiOrchestrator';
import { contextAssemblyService } from '../../services/ContextAssemblyService';
import { empathyMandateService } from '../../services/EmpathyMandateService';
// Import base types
import type {
  AgentRequest,
  PatientContext
} from '../BaseAgent';

// Import GP-specific types
import type {
  SOAPAssessment,
  ResponseGenerationContext,
  StructuredResponse,
  RegionalHealthContext
} from '../../types/agents';

export class GPResponseGenerationService {

  /**
   * Generate structured medical response using SOAP framework and comprehensive context
   */
  async generateStructuredMedicalResponse(
    request: AgentRequest, 
    soapAssessment: SOAPAssessment
  ): Promise<string> {
    try {
      // Check if we have assembled context for personalized response
      if (request.assembledContext) {
        return await this.generateContextAwareStructuredResponse(request, soapAssessment);
      }

      // Fallback to basic structured response if no context available
      return await this.generateBasicStructuredResponse(request, soapAssessment);

    } catch (error) {
      console.error('❌ Failed to generate structured medical response:', error);
      return this.generateFallbackResponse();
    }
  }

  /**
   * Generate context-aware structured response with rich patient information
   */
  private async generateContextAwareStructuredResponse(
    request: AgentRequest,
    soapAssessment: SOAPAssessment
  ): Promise<string> {
    const patientContext = request.patientContext;
    const assembledContext = request.assembledContext;

    // Build comprehensive context block
    const contextBlock = await this.buildComprehensiveContextBlock(
      patientContext,
      assembledContext,
      soapAssessment
    );

    // Get relevant medical guidelines using RAG
    const ragResponse = await this.retrieveRelevantGuidelines(request.userMessage);

    // Extract priority flags and recommendations
    const priorityFlags = this.extractPriorityFlags(contextBlock);
    const recommendations = this.extractRecommendations(contextBlock, ragResponse);

    // Build enhanced system prompt with rich context
    const enhancedPrompt = this.buildEnhancedSystemPrompt(
      contextBlock,
      priorityFlags,
      recommendations,
      soapAssessment,
      ragResponse
    );

    // Prepare messages for AI orchestrator
    const messages = [
      { role: 'system' as const, content: enhancedPrompt },
      { role: 'user' as const, content: request.userMessage }
    ];

    // Add conversation history if available
    if (request.conversationHistory && request.conversationHistory.length > 0) {
      const historyMessages = request.conversationHistory.slice(-5).map(msg => {
        return msg.speaker_type === 'user' 
          ? { role: 'user' as const, content: msg.content }
          : { role: 'user' as const, content: `Assistant: ${msg.content}` };
      });
      messages.splice(1, 0, ...historyMessages);
    }

    // Extract regional context for localized care
    const regionalContext = this.extractRegionalContext(patientContext);

    // Use AI orchestrator to generate context-aware response
    try {
      const aiResponse = await aiOrchestrator.generateResponse({
        messages,
        sessionId: request.sessionId,
        agentType: 'general-practitioner',
        maxTokens: 1000,
        temperature: 0.7,
        // Pass rich context to backend
        patientContext,
        assembledContext,
        regionalContext,
        medicalHistory: patientContext?.medicalHistory,
        urgencyLevel: request.urgencyLevel || 'medium'
      });

      if (aiResponse.success && aiResponse.data?.content) {
        return aiResponse.data.content;
      } else {
        console.warn('AI orchestrator failed, falling back to structured response');
        return this.buildFallbackStructuredResponse(request, soapAssessment, contextBlock);
      }
    } catch (error) {
      console.error('AI orchestrator error, falling back to structured response:', error);
      return this.buildFallbackStructuredResponse(request, soapAssessment, contextBlock);
    }
  }

  /**
   * Generate basic structured response without extensive context
   */
  private async generateBasicStructuredResponse(
    request: AgentRequest,
    soapAssessment: SOAPAssessment
  ): Promise<string> {
    // Build basic system prompt
    const systemPrompt = this.buildBasicSystemPrompt(soapAssessment);

    // Prepare messages
    const messages = [
      { role: 'system' as const, content: systemPrompt },
      { role: 'user' as const, content: request.userMessage }
    ];

    // Add recent conversation history
    if (request.conversationHistory && request.conversationHistory.length > 0) {
      const recentHistory = request.conversationHistory.slice(-3).map(msg => {
        return msg.speaker_type === 'user' 
          ? { role: 'user' as const, content: msg.content }
          : { role: 'user' as const, content: `Assistant: ${msg.content}` };
      });
      messages.splice(1, 0, ...recentHistory);
    }

    // Use AI orchestrator to generate response
    try {
      const aiResponse = await aiOrchestrator.generateResponse({
        messages,
        sessionId: request.sessionId,
        agentType: 'general-practitioner',
        maxTokens: 800,
        temperature: 0.7,
        // Pass available context even for basic responses
        patientContext: request.patientContext,
        urgencyLevel: request.urgencyLevel || 'medium'
      });

      if (aiResponse.success && aiResponse.data?.content) {
        return aiResponse.data.content;
      } else {
        console.warn('AI orchestrator failed, falling back to hardcoded response');
        return this.buildHardcodedBasicResponse(request, soapAssessment);
      }
    } catch (error) {
      console.error('AI orchestrator error, falling back to hardcoded response:', error);
      return this.buildHardcodedBasicResponse(request, soapAssessment);
    }
  }

  /**
   * Build comprehensive context block for enhanced responses
   */
  private async buildComprehensiveContextBlock(
    patientContext: PatientContext | undefined,
    assembledContext: any,
    soapAssessment: SOAPAssessment
  ): Promise<any> {
    const contextBlock: any = {
      patient: patientContext || {},
      soap: soapAssessment,
      assembled: assembledContext || {},
      timestamp: new Date().toISOString()
    };

    // Add empathy context if available
    try {
      if (patientContext) {
        // Create basic emotional context for empathy enhancement
        const empathyContext = {
          sentiment: 'neutral',
          emotional_cues: ['concern'],
          intensity: 'medium'
        };
        contextBlock.empathy = empathyContext;
      }
    } catch (error) {
      console.warn('Failed to get empathy context:', error);
    }

    return contextBlock;
  }

  /**
   * Retrieve relevant medical guidelines using RAG
   */
  private async retrieveRelevantGuidelines(userMessage: string): Promise<any> {
    try {
      // This would typically use the RAG tool
      // For now, return a mock response
      return {
        guidelines: [
          {
            title: 'Primary Care Guidelines',
            content: 'Evidence-based recommendations for primary care',
            relevance: 0.8
          }
        ]
      };
    } catch (error) {
      console.warn('Failed to retrieve guidelines:', error);
      return null;
    }
  }

  /**
   * Extract priority flags from context
   */
  private extractPriorityFlags(contextBlock: any): string[] {
    const flags: string[] = [];

    if (contextBlock.patient?.age > 65) {
      flags.push('elderly_patient');
    }

    if (contextBlock.soap?.assessment?.primaryDiagnosis?.includes('emergency')) {
      flags.push('emergency_concern');
    }

    return flags;
  }

  /**
   * Extract recommendations from context and guidelines
   */
  private extractRecommendations(contextBlock: any, ragResponse: any): string[] {
    const recommendations: string[] = [];

    // Add SOAP-based recommendations
    if (contextBlock.soap?.plan?.treatments) {
      recommendations.push(...contextBlock.soap.plan.treatments);
    }

    // Add guideline-based recommendations
    if (ragResponse?.guidelines) {
      recommendations.push('Follow evidence-based guidelines');
    }

    return recommendations;
  }

  /**
   * Build enhanced system prompt with rich context
   */
  private buildEnhancedSystemPrompt(
    contextBlock: any,
    priorityFlags: string[],
    recommendations: string[],
    soapAssessment: SOAPAssessment,
    ragResponse: any
  ): string {
    let systemPrompt = `You are Dr. Sarah Chen, a board-certified General Practitioner with 15 years of experience in primary care medicine. You provide comprehensive, compassionate healthcare guidance while maintaining the highest standards of medical ethics and patient safety.

PATIENT CONTEXT:
${JSON.stringify(contextBlock.patient, null, 2)}

SOAP ASSESSMENT:
${JSON.stringify(soapAssessment, null, 2)}

PRIORITY FLAGS: ${priorityFlags.join(', ')}

RECOMMENDATIONS: ${recommendations.join(', ')}

Please provide a comprehensive, empathetic response that addresses the patient's concerns while following evidence-based medical guidelines.`;

    return systemPrompt;
  }

  /**
   * Build basic system prompt for simple responses
   */
  private buildBasicSystemPrompt(soapAssessment: SOAPAssessment): string {
    return `You are Dr. Sarah Chen, a General Practitioner. Provide helpful medical guidance based on the following assessment:

Chief Complaint: ${soapAssessment.subjective.chiefComplaint}
Assessment: ${soapAssessment.assessment.primaryDiagnosis}

Provide clear, compassionate medical advice while emphasizing the importance of in-person medical care when appropriate.`;
  }

  /**
   * Extract regional context for localized care
   */
  private extractRegionalContext(patientContext: PatientContext | undefined): RegionalHealthContext | undefined {
    if (!patientContext?.location) {
      return undefined;
    }

    // This would typically query a regional health database
    return {
      country: patientContext.location.country || 'Unknown',
      region: patientContext.location.region || 'Unknown',
      commonDiseases: ['hypertension', 'diabetes', 'malaria'],
      seasonalFactors: ['rainy season health concerns'],
      culturalConsiderations: ['family involvement in healthcare decisions'],
      healthcareAccess: 'moderate',
      availableResources: ['local clinics', 'community health workers'],
      referralNetworks: ['district hospital', 'specialist clinics'],
      emergencyServices: {
        available: true,
        contactInfo: 'Local emergency number',
        responseTime: '15-30 minutes'
      }
    };
  }

  /**
   * Build fallback structured response when AI orchestrator fails
   */
  private buildFallbackStructuredResponse(
    request: AgentRequest,
    soapAssessment: SOAPAssessment,
    contextBlock: any
  ): string {
    const patientContext = request.patientContext;

    // Build personalized response using available context
    let response = this.buildPersonalizedGreeting(patientContext);

    // Add SOAP-guided assessment
    response += this.buildSOAPGuidedAssessment(request, soapAssessment, contextBlock);

    // Add regional health considerations
    if (patientContext?.location) {
      response += this.addRegionalHealthConsiderations(request.userMessage, patientContext.location);
    }

    // Add empathetic closing
    response += this.buildEmpathicClosing();

    return response;
  }

  /**
   * Build hardcoded basic response as last resort
   */
  private buildHardcodedBasicResponse(request: AgentRequest, soapAssessment: SOAPAssessment): string {
    return `Thank you for sharing your health concerns with me. Based on what you've described (${soapAssessment.subjective.chiefComplaint}), I understand you're looking for medical guidance.

While I can provide general health information, it's important that you consult with a healthcare provider in person for a proper examination and diagnosis. 

In the meantime, please monitor your symptoms and seek immediate medical attention if they worsen or if you develop any concerning new symptoms.

Is there anything specific about your symptoms that you'd like me to help clarify or any questions about when to seek medical care?`;
  }

  /**
   * Generate fallback response for errors
   */
  private generateFallbackResponse(): string {
    return "I apologize, but I'm experiencing some technical difficulties. For your safety, please consult with a healthcare provider directly if you have urgent medical concerns.";
  }

  /**
   * Build personalized greeting based on patient context
   */
  private buildPersonalizedGreeting(patientContext: PatientContext | undefined): string {
    if (patientContext?.name) {
      return `Hello ${patientContext.name}, thank you for reaching out about your health concerns. `;
    }
    return "Hello, thank you for sharing your health concerns with me. ";
  }

  /**
   * Build SOAP-guided assessment section
   */
  private buildSOAPGuidedAssessment(
    request: AgentRequest,
    soapAssessment: SOAPAssessment,
    contextBlock: any
  ): string {
    let assessment = `Based on what you've described, I understand you're experiencing ${soapAssessment.subjective.chiefComplaint}. `;

    if (soapAssessment.assessment.primaryDiagnosis) {
      assessment += `This could be related to ${soapAssessment.assessment.primaryDiagnosis}. `;
    }

    if (soapAssessment.plan.treatments.length > 0) {
      assessment += `Some general recommendations include: ${soapAssessment.plan.treatments.join(', ')}. `;
    }

    return assessment;
  }

  /**
   * Add regional health considerations
   */
  private addRegionalHealthConsiderations(userMessage: string, location: any): string {
    return "Please also consider any local health advisories or seasonal health concerns in your area. ";
  }

  /**
   * Build empathic closing
   */
  private buildEmpathicClosing(): string {
    return "I hope this information is helpful. Please don't hesitate to reach out if you have any other questions or concerns about your health.";
  }
}

export const gpResponseGenerationService = new GPResponseGenerationService();
