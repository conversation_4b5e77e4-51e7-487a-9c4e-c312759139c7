# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Development
```bash
npm run dev                    # Start development server on port 4028
npm run build                  # TypeScript compile + Vite build with sourcemaps
npm run build:prod            # Production build with hidden sourcemaps
npm run build:staging         # Staging build with sourcemaps
npm run serve                  # Preview production build
npm run preview:prod           # Preview production on port 4029
```

### Testing
```bash
npm test                       # Run all tests with Vitest
npm run test:coverage          # Run tests with coverage report
npm run test:ui                # Run tests with UI interface
npm run test:integration       # Run integration tests
npm run test:database          # Run database tests
npm run test:regional          # Run regional configuration tests
npm run test:e2e               # Run end-to-end workflow tests
npm run test:security          # Run security validation tests
```

### Code Quality
```bash
npm run lint                   # ESLint check
npm run lint:fix               # ESLint auto-fix
npm run type-check             # TypeScript type checking without emit
```

### Database & Migrations
```bash
npm run migrate                # Apply database migrations
npm run verify:db              # Test database connection
npm run seed-demo              # Seed demo data
npm run seed-knowledge         # Seed medical knowledge base
npm run seed-knowledge:clear   # Clear medical knowledge base
```

### Validation Scripts
```bash
npm run validate:critical-fixes     # Validate critical security fixes
npm run validate:phase3             # Validate phase 3 implementation
npm run validate:post-implementation # Post-implementation validation
```

## Architecture Overview

### Frontend Architecture
- **React 18** with functional components and modern hooks
- **Vite** for fast builds and development
- **TypeScript** with strict mode for medical safety
- **TailwindCSS** for utility-first styling with medical UI patterns
- **PWA** with offline-first architecture for African markets
- **Redux Toolkit** for state management with medical audit trails

### Medical AI System
- **Multi-agent orchestration** with specialist AI agents (GP, Cardiologist, Mental Health, etc.)
- **AI provider fallback chain**: OpenAI → Anthropic → Cohere
- **Real-time voice consultation** with speech-to-text and text-to-speech
- **Medical triage system** with emergency escalation protocols
- **Cultural adaptation service** for African healthcare contexts

### Backend Services
- **Supabase** for database with Row Level Security (RLS)
- **Express.js API server** (api/server.js) for secure operations
- **Paystack integration** for African payment processing
- **Real-time subscriptions** for live consultation updates

### Key Directories
```
src/
├── agents/           # AI specialist agents (GP, Cardiologist, etc.)
├── components/       # React components organized by feature
├── services/         # Business logic and API services
├── hooks/           # Custom React hooks
├── contexts/        # React context providers (Auth, Payment)
├── utils/           # Utility functions and helpers
├── types/           # TypeScript type definitions
└── tests/           # Test files organized by type
```

### Medical Safety Features
- **Emergency bypass protocols** for critical situations
- **Comprehensive error boundaries** with medical fallbacks
- **Audit trail logging** for all medical actions
- **Data encryption** at rest and in transit
- **Offline functionality** with background sync for poor connectivity

### Regional Localization
- **Multi-language support** for African languages (Swahili, Hausa, etc.)
- **Currency support** for NGN, GHS, KES, ZAR
- **Regional health context** adapters for different African countries
- **Cultural medicine integration** with traditional healing practices

## Important File References

### Configuration Files
- `vite.config.js` - Build configuration with PWA optimization
- `tsconfig.json` - TypeScript with strict medical safety settings
- `vitest.config.js` - Test configuration with PWA mocking
- `tailwind.config.cjs` - UI styling with medical color schemes

### Key Implementation Files
- `src/services/AgentOrchestrator.ts` - AI agent coordination
- `src/services/TriageService.js` - Medical triage algorithms
- `src/services/EmergencyEscalationService.ts` - Emergency protocols
- `src/utils/medicalDataValidator.ts` - Medical data validation
- `src/contexts/SimpleAuthContext.jsx` - Authentication state
- `VOICEHEALTH_AI_CODING_RULES.md` - Comprehensive coding standards

### Database Schema
- `supabase/migrations/` - Database migration files
- Key tables: consultations, audit_trail, medical_records, payment_plans

## Development Guidelines

### Medical Code Safety
- All medical functions must have comprehensive error handling
- Emergency escalation required for high-risk scenarios
- Medical data must be encrypted and audit-logged
- Never log raw patient data - always hash patient IDs

### Testing Requirements
- Medical scenarios must be thoroughly tested
- Emergency workflows require dedicated test coverage
- Offline functionality must be verified
- Regional configurations must be tested for all target countries

### Performance Considerations
- Optimized for low-end mobile devices common in African markets
- Audio processing chunked for real-time performance
- Lazy loading for non-critical medical components
- Background sync for offline consultation data

### Deployment
- Frontend deploys to Vercel/Netlify
- Backend API should be deployed separately for security
- Database migrations via Supabase CLI
- Environment variables required for all external services

## Critical Security Notes
- Patient data encryption is mandatory
- Audit trails required for all medical actions
- Emergency bypass protocols only for life-threatening situations
- Regional compliance with local healthcare regulations