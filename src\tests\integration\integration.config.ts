/**
 * INTEGRATION TEST CONFIGURATION
 * 
 * Configuration and utilities for running comprehensive integration tests
 * across all VoiceHealth AI modules and services.
 */

import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    name: 'VoiceHealth AI Integration Tests',
    environment: 'node',
    testTimeout: 30000, // 30 seconds for integration tests
    hookTimeout: 10000, // 10 seconds for setup/teardown
    teardownTimeout: 5000,
    
    // Test file patterns
    include: [
      'src/tests/integration/**/*.test.ts',
      'src/tests/integration/**/*.spec.ts'
    ],
    
    // Exclude patterns
    exclude: [
      'node_modules/**',
      'dist/**',
      'build/**'
    ],
    
    // Global setup
    globalSetup: './src/tests/integration/setup/globalSetup.ts',
    
    // Test sequencing
    sequence: {
      concurrent: false, // Run integration tests sequentially
      shuffle: false     // Maintain test order
    },
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage/integration',
      include: [
        'src/services/**/*.ts',
        'src/agents/**/*.ts',
        'src/utils/**/*.ts'
      ],
      exclude: [
        'src/tests/**',
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/**/*.d.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Reporter configuration
    reporters: [
      'verbose',
      'json',
      'html'
    ],
    
    // Output configuration
    outputFile: {
      json: './test-results/integration-results.json',
      html: './test-results/integration-report.html'
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../../'),
      '@services': path.resolve(__dirname, '../../services'),
      '@agents': path.resolve(__dirname, '../../agents'),
      '@utils': path.resolve(__dirname, '../../utils'),
      '@types': path.resolve(__dirname, '../../types')
    }
  },
  
  // Define constants for testing
  define: {
    __TEST_ENV__: true,
    __INTEGRATION_TEST__: true
  }
});

/**
 * Integration test configuration constants
 */
export const IntegrationTestConfig = {
  // Test timeouts
  timeouts: {
    short: 5000,      // 5 seconds
    medium: 15000,    // 15 seconds
    long: 30000,      // 30 seconds
    emergency: 2000   // 2 seconds for emergency scenarios
  },
  
  // Test data
  testData: {
    sessionIdPrefix: 'integration-test',
    userIdPrefix: 'test-user',
    defaultUrgencyLevel: 'medium' as const
  },
  
  // Service endpoints for testing
  endpoints: {
    supabase: process.env.VITE_SUPABASE_URL || 'http://localhost:54321',
    aiOrchestrator: process.env.AI_ORCHESTRATOR_URL || 'http://localhost:3001'
  },
  
  // Feature flags for testing
  features: {
    enableRealTimeTests: true,
    enableCircuitBreakerTests: true,
    enableMemoryCleanupTests: true,
    enableErrorSanitizationTests: true,
    enablePerformanceTests: true
  },
  
  // Performance thresholds
  performance: {
    maxResponseTime: 2000,        // 2 seconds
    maxEmergencyResponseTime: 2000, // 2 seconds
    maxConcurrentRequests: 10,
    maxMemoryUsageMB: 500
  },
  
  // HIPAA compliance validation
  hipaa: {
    sensitiveDataPatterns: [
      /\b\d{3}-\d{2}-\d{4}\b/g,           // SSN
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
      /\bMRN[-\s]?\d+\b/gi,               // Medical record numbers
      /\bpatient[-\s]?id[-\s]?\d+\b/gi    // Patient IDs
    ],
    requiredAuditFields: [
      'timestamp',
      'userId',
      'sessionId',
      'operation',
      'component'
    ]
  }
};

/**
 * Test environment setup utilities
 */
export class IntegrationTestEnvironment {
  private static instance: IntegrationTestEnvironment;
  private isSetup = false;
  
  static getInstance(): IntegrationTestEnvironment {
    if (!IntegrationTestEnvironment.instance) {
      IntegrationTestEnvironment.instance = new IntegrationTestEnvironment();
    }
    return IntegrationTestEnvironment.instance;
  }
  
  /**
   * Setup test environment
   */
  async setup(): Promise<void> {
    if (this.isSetup) return;
    
    console.log('🧪 Setting up integration test environment...');
    
    try {
      // Initialize test database
      await this.initializeTestDatabase();
      
      // Setup test services
      await this.setupTestServices();
      
      // Configure test monitoring
      await this.setupTestMonitoring();
      
      this.isSetup = true;
      console.log('✅ Integration test environment setup complete');
      
    } catch (error) {
      console.error('❌ Failed to setup integration test environment:', error);
      throw error;
    }
  }
  
  /**
   * Teardown test environment
   */
  async teardown(): Promise<void> {
    if (!this.isSetup) return;
    
    console.log('🧹 Tearing down integration test environment...');
    
    try {
      // Cleanup test data
      await this.cleanupTestData();
      
      // Reset services
      await this.resetTestServices();
      
      // Clear monitoring
      await this.clearTestMonitoring();
      
      this.isSetup = false;
      console.log('✅ Integration test environment teardown complete');
      
    } catch (error) {
      console.error('❌ Failed to teardown integration test environment:', error);
      throw error;
    }
  }
  
  /**
   * Initialize test database
   */
  private async initializeTestDatabase(): Promise<void> {
    // Implementation for test database initialization
    console.log('📊 Initializing test database...');
  }
  
  /**
   * Setup test services
   */
  private async setupTestServices(): Promise<void> {
    // Implementation for test services setup
    console.log('🔧 Setting up test services...');
  }
  
  /**
   * Setup test monitoring
   */
  private async setupTestMonitoring(): Promise<void> {
    // Implementation for test monitoring setup
    console.log('📊 Setting up test monitoring...');
  }
  
  /**
   * Cleanup test data
   */
  private async cleanupTestData(): Promise<void> {
    // Implementation for test data cleanup
    console.log('🗑️ Cleaning up test data...');
  }
  
  /**
   * Reset test services
   */
  private async resetTestServices(): Promise<void> {
    // Implementation for test services reset
    console.log('🔄 Resetting test services...');
  }
  
  /**
   * Clear test monitoring
   */
  private async clearTestMonitoring(): Promise<void> {
    // Implementation for test monitoring cleanup
    console.log('📊 Clearing test monitoring...');
  }
  
  /**
   * Generate test session ID
   */
  generateTestSessionId(): string {
    return `${IntegrationTestConfig.testData.sessionIdPrefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Generate test user ID
   */
  generateTestUserId(): string {
    return `${IntegrationTestConfig.testData.userIdPrefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Validate HIPAA compliance
   */
  validateHIPAACompliance(data: any): boolean {
    const dataString = JSON.stringify(data);
    
    for (const pattern of IntegrationTestConfig.hipaa.sensitiveDataPatterns) {
      if (pattern.test(dataString)) {
        console.warn('⚠️ HIPAA violation detected: Sensitive data found in response');
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * Measure performance
   */
  measurePerformance<T>(operation: () => Promise<T>): Promise<{ result: T; duration: number }> {
    return new Promise(async (resolve, reject) => {
      const startTime = Date.now();
      
      try {
        const result = await operation();
        const duration = Date.now() - startTime;
        
        resolve({ result, duration });
      } catch (error) {
        reject(error);
      }
    });
  }
}

// Export singleton instance
export const testEnvironment = IntegrationTestEnvironment.getInstance();
