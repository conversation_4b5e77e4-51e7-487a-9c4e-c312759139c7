/**
 * AUTHENTICATION SERVICE
 * 
 * Comprehensive authentication and authorization service for VoiceHealth AI
 * Implements JWT-based authentication with role-based access control (RBAC)
 * and emergency bypass mechanisms.
 * 
 * SECURITY FEATURES:
 * - JWT token management with refresh tokens
 * - Role-based access control (RBAC)
 * - Multi-factor authentication (MFA)
 * - Emergency bypass mechanisms
 * - Session management and timeout
 * - HIPAA-compliant audit logging
 * - Rate limiting and brute force protection
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { authenticator } from 'otplib';
import { circuitBreakerService } from './CircuitBreakerService';
import { wrapWithPerformanceMonitoring } from '../utils/performanceMonitoringWrapper';
import { handleServiceError } from '../utils/standardErrorHandler';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface User {
  id: string;
  email: string;
  role: UserRole;
  profile: UserProfile;
  permissions: Permission[];
  lastLogin: Date;
  mfaEnabled: boolean;
  emergencyAccess: boolean;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  title?: string;
  organization?: string;
  country: string;
  language: string;
  timezone: string;
  culturalContext?: any;
}

export type UserRole = 
  | 'patient' 
  | 'provider' 
  | 'admin' 
  | 'emergency' 
  | 'researcher' 
  | 'cultural_expert'
  | 'system';

export interface Permission {
  resource: string;
  actions: string[];
  conditions?: any;
}

export interface AuthenticationRequest {
  email: string;
  password: string;
  mfaCode?: string;
  emergencyOverride?: boolean;
  emergencyToken?: string;
  emergencyReason?: string;
  emergencyAuthorizedBy?: string;
  clientInfo?: {
    userAgent: string;
    ipAddress: string;
    deviceId?: string;
  };
}

export interface AuthenticationResponse {
  success: boolean;
  user?: User | undefined;
  accessToken?: string | undefined;
  refreshToken?: string | undefined;
  expiresIn?: number | undefined;
  mfaRequired?: boolean | undefined;
  emergencyBypass?: boolean | undefined;
  error?: string | undefined;
}

export interface TokenValidationResult {
  valid: boolean;
  user?: User | undefined;
  permissions?: Permission[] | undefined;
  expiresAt?: Date | undefined;
  error?: string | undefined;
}

export interface SessionInfo {
  sessionId: string;
  userId: string;
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
  ipAddress: string;
  userAgent: string;
  active: boolean;
}

// =====================================================
// AUTHENTICATION SERVICE CLASS
// =====================================================

class AuthenticationService {
  private supabase: SupabaseClient;
  private jwtSecret: string;
  private sessionCache: Map<string, SessionInfo> = new Map();
  private failedAttempts: Map<string, number> = new Map();
  private emergencyTokens: Map<string, Date> = new Map();

  constructor() {
    // Enforce secure environment configuration
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
    const jwtSecret = process.env.JWT_SECRET;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('SECURITY ERROR: Supabase configuration missing. Set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
    }

    if (!jwtSecret || jwtSecret.length < 32) {
      throw new Error('SECURITY ERROR: JWT_SECRET must be at least 32 characters long');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.jwtSecret = jwtSecret;

    // Apply performance monitoring to critical methods
    this.authenticate = wrapWithPerformanceMonitoring(
      this.authenticate.bind(this),
      {
        operation: 'user_authentication',
        emergencyOperation: false,
        target: 500, // Normal authentication target: 500ms
        includeMetadata: false
      },
      'AuthenticationService',
      'authenticate'
    );

    // Clean up expired sessions every 5 minutes
    setInterval(() => this.cleanupExpiredSessions(), 5 * 60 * 1000);
  }

  // =====================================================
  // AUTHENTICATION METHODS
  // =====================================================

  /**
   * Authenticate user with email and password
   */
  async authenticate(request: AuthenticationRequest): Promise<AuthenticationResponse> {
    try {
      const startTime = Date.now();

      // Check for emergency override - REQUIRES MULTI-FACTOR AUTHENTICATION
      if (request.emergencyOverride) {
        return await this.handleSecureEmergencyAuthentication(request);
      }

      // Check rate limiting
      if (this.isRateLimited(request.email)) {
        return {
          success: false,
          error: 'Too many failed attempts. Please try again later.'
        };
      }

      // Validate password strength for new registrations (sign-in bypasses for existing users)
      // This will be used during registration flows

      // Validate credentials
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: request.email,
        password: request.password
      });

      if (authError || !authData.user) {
        this.recordFailedAttempt(request.email);
        return {
          success: false,
          error: 'Invalid credentials'
        };
      }

      // Get user profile and permissions
      const user = await this.getUserProfile(authData.user.id);
      if (!user) {
        return {
          success: false,
          error: 'User profile not found'
        };
      }

      // Check if MFA is required
      if (user.mfaEnabled && !request.mfaCode) {
        return {
          success: false,
          mfaRequired: true,
          error: 'MFA code required'
        };
      }

      // Validate MFA if provided
      if (user.mfaEnabled && request.mfaCode) {
        const mfaValid = await this.validateMFA(user.id, request.mfaCode);
        if (!mfaValid) {
          return {
            success: false,
            error: 'Invalid MFA code'
          };
        }
      }

      // Generate tokens
      const accessToken = await this.generateAccessToken(user);
      const refreshToken = await this.generateRefreshToken(user);

      // Create session
      const session = await this.createSession(user, request.clientInfo);

      // Clear failed attempts
      this.failedAttempts.delete(request.email);

      // Log successful authentication
      await this.logAuthenticationEvent(user.id, 'login_success', request.clientInfo);

      const responseTime = Date.now() - startTime;
      console.log(`✅ Authentication successful for ${request.email} in ${responseTime}ms`);

      return {
        success: true,
        user,
        accessToken,
        refreshToken,
        expiresIn: 8 * 60 * 60, // 8 hours
      };

    } catch (error) {
      const errorResponse = handleServiceError(
        error,
        'AuthenticationService',
        'authenticate',
        undefined, // No userId available during authentication
        undefined  // No requestId available
      );
      return {
        success: false,
        error: errorResponse.error?.message || 'Authentication failed'
      };
    }
  }

  /**
   * Handle SECURE emergency authentication with multi-factor verification
   */
  private async handleSecureEmergencyAuthentication(request: AuthenticationRequest): Promise<AuthenticationResponse> {
    try {
      const startTime = Date.now();

      // SECURITY: Emergency access requires ALL of the following:
      // 1. Valid email/password credentials
      // 2. Valid emergency authorization token
      // 3. Emergency reason documentation
      // 4. Administrative approval (if not life-threatening)

      if (!request.emergencyToken || !request.emergencyReason) {
        return {
          success: false,
          error: 'Emergency access requires authorization token and documented reason'
        };
      }

      // Validate base credentials first
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: request.email,
        password: request.password
      });

      if (authError || !authData.user) {
        return {
          success: false,
          error: 'Invalid credentials - emergency access denied'
        };
      }

      // Get user profile and verify emergency privileges
      const user = await this.getUserProfile(authData.user.id);
      if (!user) {
        return {
          success: false,
          error: 'User profile not found - emergency access denied'
        };
      }

      // Verify user has emergency access privileges
      if (!user.emergencyAccess && user.role !== 'emergency' && user.role !== 'admin') {
        return {
          success: false,
          error: 'User not authorized for emergency access'
        };
      }

      // Validate emergency authorization token
      const isValidEmergencyToken = await this.validateEmergencyToken(request.emergencyToken, request.emergencyAuthorizedBy);
      if (!isValidEmergencyToken) {
        return {
          success: false,
          error: 'Invalid emergency authorization token'
        };
      }

      // Log emergency access attempt with full audit trail
      await this.logEmergencyAccess(user.id, request.emergencyReason, request.emergencyAuthorizedBy, request.clientInfo);

      // Generate emergency access token with shortened expiry and special claims
      const emergencyAccessToken = await this.generateEmergencyAccessToken(user, request.emergencyReason);

      const responseTime = Date.now() - startTime;
      console.log(`🚨 SECURE emergency authentication completed for ${request.email} in ${responseTime}ms`);

      return {
        success: true,
        user: { ...user, emergencyAccess: true },
        accessToken: emergencyAccessToken,
        expiresIn: 1 * 60 * 60, // 1 hour only for emergency access
        emergencyBypass: true
      };

    } catch (error) {
      const errorResponse = handleServiceError(
        error,
        'AuthenticationService',
        'handleSecureEmergencyAuthentication',
        undefined,
        undefined
      );
      return {
        success: false,
        error: errorResponse.error?.message || 'Emergency authentication failed'
      };
    }
  }

  /**
   * Validate emergency authorization token
   */
  private async validateEmergencyToken(token: string, authorizedBy?: string): Promise<boolean> {
    try {
      // Check if token exists in emergency authorization system
      const { data, error } = await this.supabase
        .from('emergency_authorizations')
        .select('*')
        .eq('token', token)
        .eq('status', 'active')
        .gte('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        console.error('❌ Invalid emergency token:', token);
        return false;
      }

      // Verify authorizing party if provided
      if (authorizedBy && data.authorized_by !== authorizedBy) {
        console.error('❌ Emergency token authorized by mismatch');
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ Emergency token validation error:', error);
      return false;
    }
  }

  /**
   * Generate emergency access token with special claims
   */
  private async generateEmergencyAccessToken(user: User, emergencyReason: string): Promise<string> {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      emergencyAccess: true,
      emergencyReason,
      emergencyTimestamp: Date.now(),
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (1 * 60 * 60), // 1 hour only
      iss: 'voicehealth-ai-emergency',
      aud: 'voicehealth-medical-app'
    };

    return jwt.sign(payload, this.jwtSecret, {
      algorithm: 'HS256'
    });
  }

  /**
   * Log emergency access with comprehensive audit trail
   */
  private async logEmergencyAccess(
    userId: string,
    reason: string,
    authorizedBy?: string,
    clientInfo?: any
  ): Promise<void> {
    try {
      await this.supabase
        .from('emergency_access_logs')
        .insert({
          user_id: userId,
          reason,
          authorized_by: authorizedBy,
          ip_address: clientInfo?.ipAddress,
          user_agent: clientInfo?.userAgent,
          timestamp: new Date().toISOString(),
          severity: 'critical'
        });

      // Also log in standard audit system
      await this.logAuthenticationEvent(userId, 'emergency_access', clientInfo);
    } catch (error) {
      console.error('❌ Error logging emergency access:', error);
    }
  }

  /**
   * Validate JWT token and return user information
   */
  async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      // Use circuit breaker for token validation
      const circuitBreaker = circuitBreakerService.getCircuitBreaker('token_validation');
      const result = await circuitBreaker.execute(
        async (): Promise<TokenValidationResult> => {
          try {
            // Verify JWT token with proper cryptographic validation
            const decoded = jwt.verify(token, this.jwtSecret, {
              algorithms: ['HS256'],
              issuer: 'voicehealth-ai',
              audience: 'voicehealth-medical-app'
            }) as any;

            // Get user profile from database
            const user = await this.getUserProfile(decoded.userId);
            if (!user) {
              return {
                valid: false,
                error: 'User not found'
              };
            }

            // Check if user is still active
            if (user.status !== 'active') {
              return {
                valid: false,
                error: 'User account inactive'
              };
            }

            return {
              valid: true,
              user,
              permissions: user.permissions,
              expiresAt: new Date(decoded.exp * 1000)
            };

          } catch (jwtError) {
            return {
              valid: false,
              error: jwtError instanceof Error ? jwtError.message : 'Invalid token'
            };
          }
        }
      );
      return result as TokenValidationResult;

    } catch (error) {
      console.error('❌ Token validation error:', error);
      return {
        valid: false,
        error: 'Token validation failed'
      };
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();

      if (error || !user) {
        return null;
      }

      // Get full user profile
      const userProfile = await this.getUserProfile(user.id);
      return userProfile;

    } catch (error) {
      console.error('❌ Error getting current user:', error);
      return null;
    }
  }

  /**
   * Get current session information
   */
  async getSession(): Promise<{ data: { session: any } | null; error: any }> {
    try {
      return await this.supabase.auth.getSession();
    } catch (error) {
      console.error('❌ Error getting session:', error);
      return { data: null, error };
    }
  }

  /**
   * Check if user has permission for specific resource and action
   */
  async hasPermission(userOrId: User | string, resource: string, action: string): Promise<boolean> {
    try {
      let user: User | null;

      if (typeof userOrId === 'string') {
        // If userId is provided, get the user profile
        user = await this.getUserProfile(userOrId);
        if (!user) {
          return false;
        }
      } else {
        // If User object is provided, use it directly
        user = userOrId;
      }

      // Emergency users have special permissions
      if (user.emergencyAccess) {
        return ['emergency_protocols', 'patient_data'].includes(resource);
      }

      // Admin users have all permissions
      if (user.role === 'admin' || user.role === 'system') {
        return true;
      }

      // Check specific permissions
      return user.permissions.some(permission =>
        permission.resource === resource &&
        permission.actions.includes(action)
      );

    } catch (error) {
      console.error('❌ Permission check error:', error);
      return false;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthenticationResponse> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession({
        refresh_token: refreshToken
      });

      if (error || !data.user) {
        return {
          success: false,
          error: 'Invalid refresh token'
        };
      }

      const user = await this.getUserProfile(data.user.id);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const newAccessToken = await this.generateAccessToken(user);

      return {
        success: true,
        user,
        accessToken: newAccessToken,
        refreshToken: data.session?.refresh_token || undefined,
        expiresIn: 8 * 60 * 60 // 8 hours
      };

    } catch (error) {
      console.error('❌ Token refresh error:', error);
      return {
        success: false,
        error: 'Token refresh failed'
      };
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(userId: string, sessionId?: string): Promise<boolean> {
    try {
      // Sign out from Supabase
      await this.supabase.auth.signOut();

      // Remove session from cache
      if (sessionId) {
        this.sessionCache.delete(sessionId);
      }

      // Log logout event
      await this.logAuthenticationEvent(userId, 'logout', undefined);

      console.log(`👋 User ${userId} logged out successfully`);
      return true;

    } catch (error) {
      console.error('❌ Logout error:', error);
      return false;
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private async getUserProfile(userId: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !data) {
        return null;
      }

      return {
        id: data.id,
        email: data.email,
        role: data.role,
        profile: data.profile,
        permissions: data.permissions || [],
        lastLogin: new Date(data.last_login),
        mfaEnabled: data.mfa_enabled || false,
        emergencyAccess: data.emergency_access || false,
        status: data.status || 'active'
      };

    } catch (error) {
      console.error('❌ Error getting user profile:', error);
      return null;
    }
  }

  private async generateAccessToken(user: User, expiresIn: number = 8 * 60 * 60): Promise<string> {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      emergencyAccess: user.emergencyAccess,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + expiresIn,
      iss: 'voicehealth-ai',
      aud: 'voicehealth-medical-app'
    };

    // Use proper JWT signing with HS256 algorithm
    return jwt.sign(payload, this.jwtSecret, {
      algorithm: 'HS256'
    });
  }

  private async generateRefreshToken(user: User): Promise<string> {
    // Generate secure refresh token
    return `refresh_${user.id}_${Date.now()}_${Math.random().toString(36)}`;
  }

  private generateEmergencyToken(): string {
    const token = `emergency_${Date.now()}_${Math.random().toString(36)}`;
    this.emergencyTokens.set(token, new Date(Date.now() + 2 * 60 * 60 * 1000)); // 2 hours
    return token;
  }

  private async validateMFA(userId: string, code: string): Promise<boolean> {
    try {
      // Get user's MFA secret from database
      const { data, error } = await this.supabase
        .from('user_mfa_secrets')
        .select('totp_secret')
        .eq('user_id', userId)
        .single();

      if (error || !data?.totp_secret) {
        console.error('❌ MFA secret not found for user:', userId);
        return false;
      }

      // Validate TOTP code with time window tolerance
      const isValid = authenticator.check(code, data.totp_secret);
      
      if (isValid) {
        // Log successful MFA validation
        await this.logAuthenticationEvent(userId, 'mfa_success', undefined);
        return true;
      } else {
        // Log failed MFA attempt
        await this.logAuthenticationEvent(userId, 'mfa_failure', undefined);
        return false;
      }
    } catch (error) {
      console.error('❌ MFA validation error:', error);
      await this.logAuthenticationEvent(userId, 'mfa_error', undefined);
      return false;
    }
  }

  /**
   * Generate TOTP secret for new user MFA setup
   */
  async generateMFASecret(userId: string): Promise<{ secret: string; qrCode: string }> {
    const secret = authenticator.generateSecret();
    const service = 'VoiceHealth AI';
    const account = `User:${userId}`;
    
    // Generate QR code data
    const otpauth = authenticator.keyuri(account, service, secret);
    
    // Store secret in database
    await this.supabase
      .from('user_mfa_secrets')
      .upsert({
        user_id: userId,
        totp_secret: secret,
        last_activity: new Date().toISOString()
      });

    return {
      secret,
      qrCode: otpauth
    };
  }

  /**
   * Validate password strength for HIPAA compliance
   */
  validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 12) {
      errors.push('Password must be at least 12 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    // Check for common patterns
    const commonPatterns = [
      /(.)\1{3,}/, // More than 3 repeated characters
      /1234|abcd|qwerty/i, // Common sequences
      /password|admin|user|medical|health/i // Common words
    ];
    
    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        errors.push('Password contains common patterns and is not secure');
        break;
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Hash password securely using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12; // High security for medical data
    return await bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }

  private isRateLimited(email: string): boolean {
    const attempts = this.failedAttempts.get(email) || 0;
    return attempts >= 5; // Max 5 failed attempts
  }

  private recordFailedAttempt(email: string): void {
    const attempts = this.failedAttempts.get(email) || 0;
    this.failedAttempts.set(email, attempts + 1);
    
    // Progressive lockout: longer delays for more attempts
    const lockoutDuration = Math.min(attempts * 5 * 60 * 1000, 2 * 60 * 60 * 1000); // Max 2 hours
    
    setTimeout(() => {
      this.failedAttempts.delete(email);
    }, lockoutDuration);

    // Log suspicious activity
    if (attempts >= 3) {
      console.warn(`🔒 Suspicious login activity for ${email}: ${attempts + 1} failed attempts`);
      // In production, this should trigger additional security measures
    }
  }

  /**
   * Enhanced rate limiting with IP-based tracking
   */
  private isIPRateLimited(ipAddress: string): boolean {
    // This would typically use Redis or database in production
    // For now, implement basic in-memory tracking
    const ipAttempts = this.failedAttempts.get(`ip:${ipAddress}`) || 0;
    return ipAttempts >= 10; // Max 10 attempts per IP
  }

  private recordIPFailedAttempt(ipAddress: string): void {
    const key = `ip:${ipAddress}`;
    const attempts = this.failedAttempts.get(key) || 0;
    this.failedAttempts.set(key, attempts + 1);
    
    // Clear IP-based rate limiting after 1 hour
    setTimeout(() => {
      this.failedAttempts.delete(key);
    }, 60 * 60 * 1000);
  }

  private async createSession(user: User, clientInfo?: any): Promise<SessionInfo> {
    const session: SessionInfo = {
      sessionId: `session_${user.id}_${Date.now()}`,
      userId: user.id,
      createdAt: new Date(),
      lastActivity: new Date(),
      expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000), // 8 hours
      ipAddress: clientInfo?.ipAddress || 'unknown',
      userAgent: clientInfo?.userAgent || 'unknown',
      active: true
    };

    this.sessionCache.set(session.sessionId, session);
    return session;
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    for (const [sessionId, session] of this.sessionCache.entries()) {
      if (session.expiresAt < now) {
        this.sessionCache.delete(sessionId);
      }
    }
  }

  /**
   * Get cached token for performance testing and optimization - SECURE IMPLEMENTATION
   */
  async getCachedToken(userId?: string | undefined, emergencyContext?: any | undefined): Promise<{
    success: boolean;
    token?: string | undefined;
    error?: string | undefined;
    responseTime: number;
  }> {
    const startTime = performance.now();

    try {
      // Import secure token storage
      const { secureTokenStorage } = await import('./SecureTokenStorage');

      // Get cached token if available and valid
      const cachedToken = await secureTokenStorage.getToken('access');

      if (cachedToken) {
        // Validate token metadata
        const metadata = await secureTokenStorage.getTokenMetadata();
        if (metadata && (userId ? metadata.userId === userId : true)) {
          const responseTime = performance.now() - startTime;
          return {
            success: true,
            token: cachedToken,
            responseTime
          };
        }
      }

      // If no cached token, get fresh session
      const { data: { session }, error } = await this.supabase.auth.getSession();

      if (error || !session?.access_token) {
        const responseTime = performance.now() - startTime;
        return {
          success: false,
          error: error?.message || 'No valid session found',
          responseTime
        };
      }

      // Cache the token securely for future use
      if (userId && session.user) {
        await secureTokenStorage.storeToken(
          session.access_token,
          'access',
          {
            userId: userId,
            tokenType: 'access',
            issuedAt: Date.now(),
            expiresAt: Date.now() + (8 * 60 * 60 * 1000), // 8 hours
            userAgent: navigator.userAgent
          }
        );
      }

      const responseTime = performance.now() - startTime;
      return {
        success: true,
        token: session.access_token,
        responseTime
      };

    } catch (error) {
      const responseTime = performance.now() - startTime;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime
      };
    }
  }

  /**
   * Secure logout with comprehensive token cleanup
   */
  async secureLogout(userId: string, sessionId?: string): Promise<boolean> {
    try {
      // Sign out from Supabase
      await this.supabase.auth.signOut();

      // Clear secure token storage
      const { secureTokenStorage } = await import('./SecureTokenStorage');
      secureTokenStorage.clearTokens();

      // Remove session from cache
      if (sessionId) {
        this.sessionCache.delete(sessionId);
      }

      // Clear any failed attempts
      this.failedAttempts.delete(userId);

      // Log logout event
      await this.logAuthenticationEvent(userId, 'user_logout', undefined);

      console.log(`👋 User ${userId} logged out securely`);
      return true;

    } catch (error) {
      console.error('❌ Secure logout error:', error);
      return false;
    }
  }

  private async logAuthenticationEvent(
    userId: string,
    event: string,
    clientInfo?: any
  ): Promise<void> {
    try {
      await this.supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          event_type: 'authentication',
          action: event,
          ip_address: clientInfo?.ipAddress,
          user_agent: clientInfo?.userAgent,
          severity: event.includes('emergency') ? 'critical' : 'medium',
          description: `Authentication event: ${event}`,
          metadata: { clientInfo }
        });
    } catch (error) {
      console.error('❌ Error logging authentication event:', error);
    }
  }
}

// Export singleton instance
export const authenticationService = new AuthenticationService();
export default authenticationService;
