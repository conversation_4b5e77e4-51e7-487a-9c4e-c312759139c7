import { supabase } from './supabaseClient';
import pwaService from './pwaService';
import offlineHealthRecordsService from './offlineHealthRecordsService';
import medicalDataValidator from './medicalDataValidator';
import auditLogger from './auditLogger';

const enhancedMedicalDataService = {
  // Medical Conditions with offline support
  getUserConditions: async (userId) => {
    try {
      // AUDIT: Log medical data access attempt
      await auditLogger.logConditionAccess('read', `user_${userId}_conditions`, true, {
        operation: 'get_user_conditions',
        user_id: userId,
        source: 'online_attempt'
      });

      // Try online first
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medical_conditions')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (error) {
          // AUDIT: Log failed access
          await auditLogger.logConditionAccess('read', `user_${userId}_conditions`, false, {
            operation: 'get_user_conditions',
            user_id: userId,
            error_code: error.code,
            error_message: error.message,
            source: 'online'
          });
          throw new Error(error.message);
        }

        // AUDIT: Log successful access
        await auditLogger.logConditionAccess('read', `user_${userId}_conditions`, true, {
          operation: 'get_user_conditions',
          user_id: userId,
          record_count: data.length,
          source: 'online'
        });

        // Store in offline storage for future use
        for (const condition of data) {
          await offlineHealthRecordsService.storeMedicalCondition(condition, 'sync');
        }

        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online fetch failed, trying offline:', error.message);
      
      // Fallback to offline data
      const offlineResult = await offlineHealthRecordsService.getMedicalConditions(userId);
      
      if (offlineResult.success && offlineResult.data?.length > 0) {
        return { 
          ...offlineResult, 
          source: 'offline',
          message: 'Showing offline data. Changes will sync when online.'
        };
      }

      // No offline data available
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to load medical conditions',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  addCondition: async (userId, conditionData) => {
    // SECURITY: Validate and sanitize all input data
    const validation = medicalDataValidator.validateMedicalConditionData(conditionData);

    if (!validation.valid) {
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`,
        validationErrors: validation.errors
      };
    }

    // Use sanitized data
    const sanitizedData = validation.sanitized;

    const conditionWithId = {
      id: Math.random().toString(36).substr(2, 9), // Temporary ID for offline
      user_id: userId,
      condition_name: sanitizedData.condition_name,
      diagnosed_date: sanitizedData.diagnosed_date,
      is_current: conditionData.is_current !== false,
      severity: sanitizedData.severity,
      notes: sanitizedData.notes,
      created_at: new Date().toISOString(),
      _warnings: sanitizedData._warnings // Include validation warnings
    };

    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medical_conditions')
          .insert({
            user_id: userId,
            condition_name: sanitizedData.condition_name,
            diagnosed_date: sanitizedData.diagnosed_date,
            is_current: conditionData.is_current !== false,
            severity: sanitizedData.severity,
            notes: sanitizedData.notes,
            created_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          // AUDIT: Log failed condition creation
          await auditLogger.logConditionAccess('create', 'new_condition', false, {
            operation: 'add_condition',
            user_id: userId,
            condition_name: sanitizedData.condition_name,
            error_code: error.code,
            error_message: error.message,
            source: 'online'
          });
          throw new Error(error.message);
        }

        // AUDIT: Log successful condition creation
        await auditLogger.logConditionAccess('create', data.id, true, {
          operation: 'add_condition',
          user_id: userId,
          condition_name: sanitizedData.condition_name,
          severity: sanitizedData.severity,
          source: 'online'
        });

        // Store in offline storage
        await offlineHealthRecordsService.storeMedicalCondition(data, 'sync');

        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online add failed, storing offline:', error.message);
      
      // Store offline for later sync
      const offlineResult = await offlineHealthRecordsService.storeMedicalCondition(
        conditionWithId, 
        'create'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          data: conditionWithId,
          source: 'offline',
          message: 'Condition saved offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to add medical condition',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  updateCondition: async (conditionId, updates) => {
    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medical_conditions')
          .update(updates)
          .eq('id', conditionId)
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        // Update offline storage
        await offlineHealthRecordsService.storeMedicalCondition(data, 'sync');
        
        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online update failed, storing offline:', error.message);
      
      // Update offline data
      const updatedCondition = { id: conditionId, ...updates };
      const offlineResult = await offlineHealthRecordsService.storeMedicalCondition(
        updatedCondition, 
        'update'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          data: updatedCondition,
          source: 'offline',
          message: 'Condition updated offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to update medical condition',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  deleteCondition: async (conditionId) => {
    try {
      if (pwaService.isAppOnline()) {
        const { error } = await supabase
          .from('medical_conditions')
          .delete()
          .eq('id', conditionId);

        if (error) {
          throw new Error(error.message);
        }

        // Mark as deleted in offline storage
        await offlineHealthRecordsService.storeMedicalCondition(
          { id: conditionId }, 
          'delete'
        );
        
        return { success: true, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online delete failed, storing offline:', error.message);
      
      // Mark as deleted offline
      const offlineResult = await offlineHealthRecordsService.storeMedicalCondition(
        { id: conditionId }, 
        'delete'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          source: 'offline',
          message: 'Condition marked for deletion offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to delete medical condition',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  // Medications with offline support
  getUserMedications: async (userId) => {
    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medications')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (error) {
          throw new Error(error.message);
        }

        // Store in offline storage
        for (const medication of data) {
          await offlineHealthRecordsService.storeMedication(medication, 'sync');
        }

        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online fetch failed, trying offline:', error.message);
      
      const offlineResult = await offlineHealthRecordsService.getMedications(userId);
      
      if (offlineResult.success && offlineResult.data?.length > 0) {
        return { 
          ...offlineResult, 
          source: 'offline',
          message: 'Showing offline data. Changes will sync when online.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to load medications',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  addMedication: async (userId, medicationData) => {
    // SECURITY: Validate and sanitize all input data
    const validation = medicalDataValidator.validateMedicationData(medicationData);

    if (!validation.valid) {
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`,
        validationErrors: validation.errors
      };
    }

    // Use sanitized data
    const sanitizedData = validation.sanitized;

    const medicationWithId = {
      id: Math.random().toString(36).substr(2, 9),
      user_id: userId,
      medication_name: sanitizedData.medication_name,
      dosage: sanitizedData.dosage,
      frequency: sanitizedData.frequency,
      start_date: sanitizedData.start_date,
      end_date: medicationData.end_date,
      is_current: medicationData.is_current !== false,
      prescribed_by: medicationData.prescribed_by,
      notes: sanitizedData.notes,
      created_at: new Date().toISOString(),
      _warnings: sanitizedData._warnings // Include validation warnings
    };

    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medications')
          .insert({
            user_id: userId,
            medication_name: sanitizedData.medication_name,
            dosage: sanitizedData.dosage,
            frequency: sanitizedData.frequency,
            start_date: sanitizedData.start_date,
            end_date: medicationData.end_date,
            is_current: medicationData.is_current !== false,
            prescribed_by: medicationData.prescribed_by,
            notes: sanitizedData.notes,
            created_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        await offlineHealthRecordsService.storeMedication(data, 'sync');
        
        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online add failed, storing offline:', error.message);
      
      const offlineResult = await offlineHealthRecordsService.storeMedication(
        medicationWithId, 
        'create'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          data: medicationWithId,
          source: 'offline',
          message: 'Medication saved offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to add medication',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  updateMedication: async (medicationId, updates) => {
    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('medications')
          .update(updates)
          .eq('id', medicationId)
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        await offlineHealthRecordsService.storeMedication(data, 'sync');
        
        return { success: true, data, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online update failed, storing offline:', error.message);
      
      const updatedMedication = { id: medicationId, ...updates };
      const offlineResult = await offlineHealthRecordsService.storeMedication(
        updatedMedication, 
        'update'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          data: updatedMedication,
          source: 'offline',
          message: 'Medication updated offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to update medication',
        offline: !pwaService.isAppOnline()
      };
    }
  },

  deleteMedication: async (medicationId) => {
    try {
      if (pwaService.isAppOnline()) {
        const { error } = await supabase
          .from('medications')
          .delete()
          .eq('id', medicationId);

        if (error) {
          throw new Error(error.message);
        }

        await offlineHealthRecordsService.storeMedication(
          { id: medicationId }, 
          'delete'
        );
        
        return { success: true, source: 'online' };
      } else {
        throw new Error('Offline mode');
      }
    } catch (error) {
      console.log('Online delete failed, storing offline:', error.message);
      
      const offlineResult = await offlineHealthRecordsService.storeMedication(
        { id: medicationId }, 
        'delete'
      );
      
      if (offlineResult.success) {
        return {
          success: true,
          source: 'offline',
          message: 'Medication marked for deletion offline. Will sync when connection is restored.'
        };
      }

      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.',
          offline: true
        };
      }
      
      return { 
        success: false, 
        error: 'Failed to delete medication',
        offline: !pwaService.isAppOnline()
      };
    }
  }
};

export default enhancedMedicalDataService;