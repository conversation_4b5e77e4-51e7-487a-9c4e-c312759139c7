/**
 * MEDICAL ERROR BOUNDARY
 * 
 * Specialized error boundary for medical data operations that prioritizes
 * patient safety and data preservation above all else.
 * 
 * PATIENT SAFETY FEATURES:
 * - Preserves medical data during errors
 * - Provides emergency fallback interfaces
 * - Logs all errors for medical review
 * - Maintains audit trail for compliance
 * - Offers data recovery mechanisms
 * - Emergency contact integration
 * 
 * CRITICAL REQUIREMENTS:
 * - Never lose patient data during errors
 * - Always provide emergency access to critical functions
 * - Log all errors with medical context
 * - Maintain HIPAA compliance during error handling
 * - Provide clear recovery paths for medical staff
 */

import React, { Component, ReactNode, ErrorInfo } from 'react';
import { MedicalDataError } from '../../types';
import auditLogger from '../../utils/auditLogger';

interface MedicalErrorBoundaryState {
  readonly hasError: boolean;
  readonly error: Error | null;
  readonly errorInfo: ErrorInfo | null;
  readonly errorId: string | null;
  readonly retryCount: number;
  readonly preservedData: unknown | null;
  readonly emergencyMode: boolean;
  readonly recoveryOptions: RecoveryOption[];
}

interface RecoveryOption {
  readonly id: string;
  readonly label: string;
  readonly description: string;
  readonly action: () => void;
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly requiresConfirmation: boolean;
}

interface MedicalErrorBoundaryProps {
  readonly children: ReactNode;
  readonly componentName: string;
  readonly medicalContext?: {
    readonly patientId?: string;
    readonly dataType?: string;
    readonly criticalOperation?: boolean;
    readonly emergencyContact?: string;
  };
  readonly onError?: (error: Error, errorInfo: ErrorInfo, preservedData?: unknown) => void;
  readonly onRecovery?: (recoveryMethod: string) => void;
  readonly fallbackComponent?: ReactNode;
  readonly enableEmergencyMode?: boolean;
  readonly preserveDataOnError?: boolean;
}

class MedicalErrorBoundary extends Component<MedicalErrorBoundaryProps, MedicalErrorBoundaryState> {
  private preservedDataRef: React.RefObject<unknown>;
  private emergencyTimer: NodeJS.Timeout | null;

  constructor(props: MedicalErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      preservedData: null,
      emergencyMode: false,
      recoveryOptions: []
    };

    this.preservedDataRef = React.createRef();
    this.emergencyTimer = null;
  }

  static getDerivedStateFromError(error: Error): Partial<MedicalErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: crypto.randomUUID()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = crypto.randomUUID();
    
    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Preserve any medical data that might be lost
    this.preserveMedicalData();

    // Log error with medical context
    this.logMedicalError(error, errorInfo, errorId);

    // Determine if emergency mode should be activated
    this.evaluateEmergencyMode(error);

    // Generate recovery options
    this.generateRecoveryOptions(error);

    // Notify parent component
    this.props.onError?.(error, errorInfo, this.state.preservedData);

    // Set emergency timer for critical operations
    if (this.props.medicalContext?.criticalOperation) {
      this.startEmergencyTimer();
    }
  }

  /**
   * Preserve medical data to prevent loss during errors
   */
  private preserveMedicalData(): void {
    if (!this.props.preserveDataOnError) return;

    try {
      // Attempt to extract data from various sources
      const preservedData = {
        formData: this.extractFormData(),
        localStorage: this.extractLocalStorageData(),
        sessionData: this.extractSessionData(),
        timestamp: new Date().toISOString()
      };

      this.setState({ preservedData });

      // Store in emergency backup
      localStorage.setItem(
        `medical_error_backup_${this.state.errorId}`,
        JSON.stringify(preservedData)
      );
    } catch (preservationError) {
      console.error('Failed to preserve medical data:', preservationError);
    }
  }

  /**
   * Extract form data from the DOM
   */
  private extractFormData(): Record<string, unknown> {
    const formData: Record<string, unknown> = {};
    
    try {
      const forms = document.querySelectorAll('form');
      forms.forEach((form, index) => {
        const data = new FormData(form);
        const formObject: Record<string, unknown> = {};
        
        for (const [key, value] of data.entries()) {
          formObject[key] = value;
        }
        
        if (Object.keys(formObject).length > 0) {
          formData[`form_${index}`] = formObject;
        }
      });
    } catch (error) {
      console.warn('Failed to extract form data:', error);
    }

    return formData;
  }

  /**
   * Extract relevant medical data from localStorage
   */
  private extractLocalStorageData(): Record<string, unknown> {
    const medicalData: Record<string, unknown> = {};
    
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('medical') || key.includes('patient') || key.includes('condition'))) {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              medicalData[key] = JSON.parse(value);
            } catch {
              medicalData[key] = value;
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to extract localStorage data:', error);
    }

    return medicalData;
  }

  /**
   * Extract session data
   */
  private extractSessionData(): Record<string, unknown> {
    const sessionData: Record<string, unknown> = {};
    
    try {
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.includes('medical') || key.includes('patient'))) {
          const value = sessionStorage.getItem(key);
          if (value) {
            try {
              sessionData[key] = JSON.parse(value);
            } catch {
              sessionData[key] = value;
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to extract session data:', error);
    }

    return sessionData;
  }

  /**
   * Log medical error with comprehensive context
   */
  private async logMedicalError(error: Error, errorInfo: ErrorInfo, errorId: string): Promise<void> {
    try {
      const medicalError: MedicalDataError = new MedicalDataError(
        error.message,
        'COMPONENT_ERROR',
        this.determineSeverity(error),
        this.props.medicalContext?.criticalOperation || false
      );

      await auditLogger.logMedicalDataAccess(
        'error_occurred',
        'medical_component',
        errorId,
        {
          component_name: this.props.componentName,
          error_message: error.message,
          error_stack: error.stack,
          error_info: errorInfo,
          medical_context: this.props.medicalContext,
          patient_id: this.props.medicalContext?.patientId,
          data_type: this.props.medicalContext?.dataType,
          critical_operation: this.props.medicalContext?.criticalOperation,
          preserved_data_available: !!this.state.preservedData,
          retry_count: this.state.retryCount,
          user_agent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        }
      );
    } catch (loggingError) {
      console.error('Failed to log medical error:', loggingError);
    }
  }

  /**
   * Determine error severity based on medical context
   */
  private determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    if (this.props.medicalContext?.criticalOperation) {
      return 'critical';
    }

    if (error.message.includes('encryption') || error.message.includes('data loss')) {
      return 'high';
    }

    if (error.message.includes('network') || error.message.includes('timeout')) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Evaluate if emergency mode should be activated
   */
  private evaluateEmergencyMode(error: Error): void {
    const shouldActivateEmergency = 
      this.props.enableEmergencyMode &&
      (this.props.medicalContext?.criticalOperation ||
       this.determineSeverity(error) === 'critical' ||
       error.message.includes('data loss'));

    if (shouldActivateEmergency) {
      this.setState({ emergencyMode: true });
    }
  }

  /**
   * Generate context-appropriate recovery options
   */
  private generateRecoveryOptions(error: Error): void {
    const options: RecoveryOption[] = [
      {
        id: 'retry',
        label: 'Retry Operation',
        description: 'Attempt to retry the failed operation',
        action: this.handleRetry,
        severity: 'low',
        requiresConfirmation: false
      },
      {
        id: 'refresh',
        label: 'Refresh Page',
        description: 'Reload the page to reset the application state',
        action: this.handleRefresh,
        severity: 'medium',
        requiresConfirmation: true
      }
    ];

    if (this.state.preservedData) {
      options.push({
        id: 'restore',
        label: 'Restore Data',
        description: 'Restore preserved medical data from before the error',
        action: this.handleDataRestore,
        severity: 'medium',
        requiresConfirmation: false
      });
    }

    if (this.props.medicalContext?.emergencyContact) {
      options.push({
        id: 'emergency',
        label: 'Contact Emergency Support',
        description: 'Contact emergency medical IT support',
        action: this.handleEmergencyContact,
        severity: 'high',
        requiresConfirmation: false
      });
    }

    this.setState({ recoveryOptions: options });
  }

  /**
   * Start emergency timer for critical operations
   */
  private startEmergencyTimer(): void {
    this.emergencyTimer = setTimeout(() => {
      if (this.state.hasError && this.props.medicalContext?.emergencyContact) {
        this.handleEmergencyContact();
      }
    }, 30000); // 30 seconds
  }

  /**
   * Handle retry operation
   */
  private handleRetry = (): void => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: prevState.retryCount + 1,
      emergencyMode: false
    }));

    this.props.onRecovery?.('retry');
  };

  /**
   * Handle page refresh
   */
  private handleRefresh = (): void => {
    this.props.onRecovery?.('refresh');
    window.location.reload();
  };

  /**
   * Handle data restoration
   */
  private handleDataRestore = (): void => {
    if (this.state.preservedData) {
      // Attempt to restore data to forms or state
      this.restoreDataToForms();
      this.props.onRecovery?.('restore');
    }
  };

  /**
   * Handle emergency contact
   */
  private handleEmergencyContact = (): void => {
    const contact = this.props.medicalContext?.emergencyContact;
    if (contact) {
      // This would typically integrate with a medical emergency system
      alert(`Emergency contact initiated: ${contact}\nError ID: ${this.state.errorId}`);
      this.props.onRecovery?.('emergency_contact');
    }
  };

  /**
   * Restore preserved data to forms
   */
  private restoreDataToForms(): void {
    try {
      const preservedData = this.state.preservedData as any;
      if (preservedData?.formData) {
        Object.entries(preservedData.formData).forEach(([formKey, formData]: [string, any]) => {
          const forms = document.querySelectorAll('form');
          const formIndex = parseInt(formKey.split('_')[1] || '0');
          const form = forms[formIndex];
          
          if (form && formData) {
            Object.entries(formData).forEach(([fieldName, fieldValue]: [string, any]) => {
              const field = form.querySelector(`[name="${fieldName}"]`) as HTMLInputElement;
              if (field && typeof fieldValue === 'string') {
                field.value = fieldValue;
              }
            });
          }
        });
      }
    } catch (error) {
      console.error('Failed to restore data to forms:', error);
    }
  }

  componentWillUnmount() {
    if (this.emergencyTimer) {
      clearTimeout(this.emergencyTimer);
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallbackComponent) {
        return this.props.fallbackComponent;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-red-50">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-6 mx-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-red-800">
                  Medical System Error
                </h3>
                <p className="text-sm text-red-600">
                  Component: {this.props.componentName}
                </p>
              </div>
            </div>

            {this.state.emergencyMode && (
              <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded">
                <p className="text-sm text-red-800 font-medium">
                  🚨 EMERGENCY MODE ACTIVATED
                </p>
                <p className="text-xs text-red-700 mt-1">
                  Critical medical operation failed. Emergency protocols initiated.
                </p>
              </div>
            )}

            <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded">
              <p className="text-sm text-gray-700">
                <strong>Error ID:</strong> {this.state.errorId}
              </p>
              <p className="text-sm text-gray-700">
                <strong>Time:</strong> {new Date().toLocaleString()}
              </p>
              {this.props.medicalContext?.patientId && (
                <p className="text-sm text-gray-700">
                  <strong>Patient Context:</strong> {this.props.medicalContext.patientId}
                </p>
              )}
              {Boolean(this.state.preservedData) && (
                <p className="text-sm text-green-700">
                  ✓ Medical data has been preserved
                </p>
              )}
            </div>

            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Recovery Options:</h4>
              <div className="space-y-2">
                {this.state.recoveryOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={option.action}
                    className={`w-full text-left p-3 rounded border ${
                      option.severity === 'critical' ? 'bg-red-50 border-red-200 hover:bg-red-100' :
                      option.severity === 'high' ? 'bg-orange-50 border-orange-200 hover:bg-orange-100' :
                      option.severity === 'medium' ? 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100' :
                      'bg-blue-50 border-blue-200 hover:bg-blue-100'
                    }`}
                  >
                    <div className="font-medium text-sm">{option.label}</div>
                    <div className="text-xs text-gray-600">{option.description}</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="text-xs text-gray-500">
              <p>This error has been logged for medical review.</p>
              <p>Retry count: {this.state.retryCount}</p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default MedicalErrorBoundary;
