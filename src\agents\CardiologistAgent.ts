/**
 * CARDIOLOGIST AGENT
 * 
 * Specialized AI agent for cardiovascular medicine and heart health.
 * Provides expert consultation on heart conditions, cardiovascular risk assessment,
 * and cardiac emergency management.
 * 
 * SPECIALIZATION AREAS:
 * - Chest pain evaluation and cardiac risk stratification
 * - Heart rhythm disorders and arrhythmia management
 * - Hypertension and blood pressure management
 * - Heart failure diagnosis and treatment
 * - Coronary artery disease assessment
 * - Cardiac emergency protocols
 * - Preventive cardiology and lifestyle counseling
 */

import { BaseAgent } from './BaseAgent';
import { RAGTool } from '../tools/RAGTool';
import type {
  AgentRequest,
  AgentResponse,
  AgentRole,
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class CardiologistAgent extends BaseAgent {
  private ragTool: RAGTool;

  constructor(memoryManager: MemoryManager) {
    const id = 'cardiologist-agent-001';
    const name = 'Dr. <PERSON>';
    const role: AgentRole = 'cardiologist';
    const capabilities: AgentCapability[] = [
      'specialist_consultation',
      'diagnostic_assessment',
      'treatment_planning',
      'emergency_response',
      'patient_education',
      'chronic_disease_management'
    ];

    // Initialize with RAG tool for knowledge retrieval
    const tools = [new RAGTool()];

    const systemPrompt = `You are Dr. Michael Rodriguez, a board-certified Cardiologist with 20 years of experience in cardiovascular medicine. You specialize in comprehensive heart care, from emergency cardiac situations to preventive cardiology.

AVAILABLE TOOLS:
- Medical Knowledge Retrieval (RAG): Access to current cardiovascular guidelines, research, and protocols

When providing consultations:
1. Use the RAG tool to retrieve relevant cardiovascular guidelines and research
2. Reference current evidence-based recommendations
3. Cite specific studies or guidelines when appropriate
4. Ensure recommendations align with latest clinical protocols

CORE EXPERTISE:
- Acute coronary syndromes and myocardial infarction
- Heart rhythm disorders and electrophysiology
- Heart failure and cardiomyopathy
- Hypertension and vascular disease
- Valvular heart disease
- Preventive cardiology and risk stratification
- Cardiac rehabilitation and lifestyle medicine

ASSESSMENT APPROACH:
- Systematic evaluation of cardiovascular symptoms
- Risk stratification using established guidelines
- Evidence-based diagnostic and treatment recommendations
- Clear communication of cardiac risks and prognosis
- Emphasis on lifestyle modifications and prevention

EMERGENCY PROTOCOLS:
- Immediate recognition of acute coronary syndromes
- STEMI and NSTEMI management protocols
- Cardiac arrest and arrhythmia emergencies
- Hypertensive crisis management
- Acute heart failure protocols

COMMUNICATION STYLE:
- Professional yet reassuring approach
- Clear explanation of cardiac conditions in patient-friendly terms
- Emphasis on both immediate care and long-term heart health
- Collaborative approach with other specialists when needed
- Strong focus on patient education and empowerment

SAFETY PRIORITIES:
- Immediate identification of life-threatening cardiac conditions
- Appropriate emergency care recommendations
- Clear guidance on when to seek immediate medical attention
- Careful medication counseling and interaction awareness
- Regular monitoring recommendations for cardiac conditions

COLLABORATION FOCUS:
- Work closely with emergency medicine for acute cases
- Coordinate with primary care for ongoing management
- Collaborate with endocrinology for diabetes and metabolic syndrome
- Partner with nutrition specialists for dietary management
- Engage mental health professionals for cardiac psychology

Remember: Cardiovascular disease remains a leading cause of mortality. Your expertise can be life-saving, but always emphasize the importance of in-person cardiac evaluation and appropriate diagnostic testing when indicated.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager, tools);

    // Store RAG tool reference for easy access
    this.ragTool = tools[0] as RAGTool;
  }

  /**
   * Handle cardiovascular consultation requests
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`❤️ Cardiologist processing request for session: ${request.sessionId}`);

      // Immediate cardiac emergency assessment
      const emergencyFlags = this.detectCardiacEmergencies(request.userMessage);
      
      // If cardiac emergency detected, prioritize emergency response
      if (emergencyFlags.some(flag => flag.severity === 'critical')) {
        return this.handleCardiacEmergency(request, emergencyFlags);
      }

      // Use RAG tool to retrieve relevant cardiovascular knowledge
      const knowledgeContext = await this.retrieveCardiovascularKnowledge(request.userMessage);

      // Assess cardiovascular risk factors
      const riskAssessment = this.assessCardiovascularRisk(request);

      // Generate enhanced cardiology response using retrieved knowledge
      const response = await this.generateEnhancedCardiologyResponse(request, riskAssessment, knowledgeContext);

      // Determine if other specialists needed
      const handoffSuggestions = this.analyzeForSpecialistCollaboration(request);

      // Calculate confidence based on cardiac complexity
      const confidence = this.calculateCardiacConfidence(request);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, confidence, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence,
        reasoning: 'Cardiovascular assessment completed with specialized cardiology expertise',
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: this.generateCardiacFollowUp(request, riskAssessment),
        metadata: {
          responseTime,
          assessmentType: 'cardiology_consultation',
          riskLevel: riskAssessment.riskLevel,
          emergencyProtocol: emergencyFlags.length > 0
        }
      };

    } catch (error) {
      console.error('❌ Cardiologist Agent error:', error);
      
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I apologize, but I'm experiencing technical difficulties. For any cardiac symptoms or concerns, please seek immediate medical attention from a cardiologist or emergency department.",
        confidence: 0.1,
        reasoning: 'Technical error during cardiology consultation',
        emergencyFlags: [{
          type: 'medical_emergency',
          severity: 'medium',
          description: 'System error - recommend direct cardiology consultation',
          recommendedAction: 'Consult cardiologist or emergency department',
          timeToResponse: 1500
        }]
      };
    }
  }

  /**
   * Detect cardiac emergencies with high sensitivity
   */
  private detectCardiacEmergencies(message: string): EmergencyFlag[] {
    const emergencyFlags: EmergencyFlag[] = [];
    const lowerMessage = message.toLowerCase();

    // Critical cardiac emergencies
    const criticalCardiacKeywords = [
      'heart attack', 'myocardial infarction', 'mi', 'crushing chest pain',
      'severe chest pain', 'chest pain radiating', 'chest pain with sweating',
      'cardiac arrest', 'no pulse', 'collapsed', 'sudden cardiac death'
    ];

    // High severity cardiac symptoms
    const highSeverityCardiacKeywords = [
      'chest pain', 'chest pressure', 'chest tightness', 'chest discomfort',
      'shortness of breath', 'difficulty breathing', 'palpitations',
      'irregular heartbeat', 'racing heart', 'heart racing', 'syncope',
      'fainting', 'dizziness with chest pain', 'jaw pain', 'arm pain'
    ];

    // Check for critical cardiac emergencies
    criticalCardiacKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'critical',
          description: `Critical cardiac emergency detected: ${keyword}`,
          recommendedAction: 'Call emergency services immediately - possible heart attack',
          timeToResponse: 500 // 0.5 seconds for cardiac emergencies
        });
      }
    });

    // Check for high severity cardiac symptoms
    if (emergencyFlags.length === 0) { // Only if no critical flags
      highSeverityCardiacKeywords.forEach(keyword => {
        if (lowerMessage.includes(keyword)) {
          emergencyFlags.push({
            type: 'medical_emergency',
            severity: 'high',
            description: `Significant cardiac symptom detected: ${keyword}`,
            recommendedAction: 'Seek immediate cardiac evaluation',
            timeToResponse: 1000 // 1 second for high severity
          });
        }
      });
    }

    return emergencyFlags;
  }

  /**
   * Handle cardiac emergency situations
   */
  private async handleCardiacEmergency(request: AgentRequest, emergencyFlags: EmergencyFlag[]): Promise<AgentResponse> {
    console.log('🚨 Cardiologist handling cardiac emergency');

    const emergencyResponse = `🚨 CARDIAC EMERGENCY DETECTED 🚨

I've identified potential cardiac emergency symptoms that require IMMEDIATE medical attention.

IMMEDIATE ACTIONS:
1. **CALL EMERGENCY SERVICES NOW**: 911 (US), 999 (UK), 112 (EU)
2. **If experiencing chest pain**: 
   - Chew 325mg aspirin if not allergic (unless told otherwise by emergency services)
   - Sit down and rest
   - Loosen tight clothing
3. **Stay on the line with emergency services**
4. **Do not drive yourself** - wait for ambulance

CRITICAL SIGNS OF HEART ATTACK:
- Chest pain, pressure, or discomfort
- Pain radiating to arm, jaw, neck, or back
- Shortness of breath
- Sweating, nausea, or lightheadedness
- Feeling of impending doom

⚠️ **TIME IS MUSCLE**: Every minute counts in cardiac emergencies. Professional emergency cardiac care is essential.

If this is not an emergency, please rephrase your concern and I'll provide specialized cardiology guidance.`;

    return {
      agentId: this.id,
      agentName: this.name,
      content: emergencyResponse,
      confidence: 0.98,
      reasoning: 'Cardiac emergency detected - immediate emergency care required',
      emergencyFlags,
      suggestedHandoffs: [{
        targetAgentRole: 'emergency',
        reason: 'Critical cardiac emergency requiring emergency medicine support',
        urgency: 'critical',
        contextToTransfer: `Cardiac emergency: ${emergencyFlags.map(f => f.description).join(', ')}`,
        patientConsent: false // Emergency override
      }],
      metadata: {
        emergencyDetected: true,
        cardiacEmergency: true,
        responseTime: Date.now(),
        priorityLevel: 'critical'
      }
    };
  }

  /**
   * Assess cardiovascular risk factors
   */
  private assessCardiovascularRisk(request: AgentRequest): CardiacRiskAssessment {
    const message = request.userMessage.toLowerCase();
    let riskScore = 0;
    const riskFactors: string[] = [];

    // Major risk factors
    const majorRiskFactors = [
      { keyword: 'diabetes', score: 3, description: 'Diabetes mellitus' },
      { keyword: 'hypertension', score: 2, description: 'High blood pressure' },
      { keyword: 'high blood pressure', score: 2, description: 'High blood pressure' },
      { keyword: 'smoking', score: 3, description: 'Tobacco use' },
      { keyword: 'high cholesterol', score: 2, description: 'Dyslipidemia' },
      { keyword: 'family history', score: 2, description: 'Family history of heart disease' }
    ];

    // Assess risk factors
    majorRiskFactors.forEach(factor => {
      if (message.includes(factor.keyword)) {
        riskScore += factor.score;
        riskFactors.push(factor.description);
      }
    });

    // Age considerations (if mentioned)
    if (message.includes('over 65') || message.includes('elderly')) {
      riskScore += 2;
      riskFactors.push('Advanced age');
    }

    // Determine risk level
    let riskLevel: 'low' | 'moderate' | 'high' | 'very_high';
    if (riskScore >= 8) riskLevel = 'very_high';
    else if (riskScore >= 5) riskLevel = 'high';
    else if (riskScore >= 3) riskLevel = 'moderate';
    else riskLevel = 'low';

    return {
      riskLevel,
      riskScore,
      riskFactors,
      recommendedActions: this.getRecommendedActions(riskLevel)
    };
  }

  /**
   * Generate specialized cardiology response
   */
  private async generateCardiologyResponse(request: AgentRequest, riskAssessment: CardiacRiskAssessment): Promise<string> {
    const hasChestPain = request.userMessage.toLowerCase().includes('chest pain') ||
                        request.userMessage.toLowerCase().includes('chest pressure');

    if (hasChestPain) {
      return `Thank you for consulting with me about your chest symptoms. As a cardiologist, I want to help you understand your symptoms and ensure you receive appropriate cardiac care.

**CHEST PAIN EVALUATION:**

Chest pain can have many causes, ranging from cardiac to non-cardiac. Important factors I consider include:

1. **Character**: Is it sharp, dull, pressure-like, or burning?
2. **Location**: Where exactly do you feel it?
3. **Radiation**: Does it spread to your arm, jaw, neck, or back?
4. **Triggers**: What brings it on? Exercise, stress, eating?
5. **Duration**: How long does it last?
6. **Associated symptoms**: Shortness of breath, sweating, nausea?

**CARDIOVASCULAR RISK ASSESSMENT:**
- Risk Level: ${riskAssessment.riskLevel.toUpperCase()}
- Risk Factors Identified: ${riskAssessment.riskFactors.join(', ') || 'None mentioned'}

**RECOMMENDATIONS:**
${riskAssessment.recommendedActions.join('\n')}

**IMPORTANT**: Chest pain requires proper medical evaluation with physical examination, ECG, and possibly cardiac testing. Please schedule an appointment with a cardiologist or your primary care physician for comprehensive evaluation.

**SEEK IMMEDIATE CARE IF YOU EXPERIENCE:**
- Severe or worsening chest pain
- Chest pain with shortness of breath, sweating, or nausea
- Pain radiating to arm, jaw, or back
- Any symptoms that concern you

How can I help you better understand your cardiac health and next steps?`;
    }

    return `Hello! I'm Dr. Michael Rodriguez, a cardiologist specializing in heart health and cardiovascular medicine.

**CARDIOVASCULAR RISK ASSESSMENT:**
- Current Risk Level: ${riskAssessment.riskLevel.toUpperCase()}
- Risk Factors: ${riskAssessment.riskFactors.join(', ') || 'None identified from our conversation'}

**MY CARDIOLOGY EXPERTISE INCLUDES:**
- Heart disease prevention and risk reduction
- Chest pain evaluation and cardiac testing
- Blood pressure and cholesterol management
- Heart rhythm disorders
- Heart failure management
- Post-cardiac event care and rehabilitation

**RECOMMENDED ACTIONS:**
${riskAssessment.recommendedActions.join('\n')}

**HEART-HEALTHY LIFESTYLE:**
- Regular aerobic exercise (as appropriate for your condition)
- Heart-healthy diet (Mediterranean-style diet)
- Stress management and adequate sleep
- Tobacco cessation if applicable
- Regular blood pressure and cholesterol monitoring

Please remember that while I can provide general cardiology guidance, a comprehensive cardiac evaluation requires in-person examination, ECG, and appropriate testing based on your individual risk factors and symptoms.

What specific cardiac concerns or questions can I help address today?`;
  }

  /**
   * Get recommended actions based on risk level
   */
  private getRecommendedActions(riskLevel: string): string[] {
    switch (riskLevel) {
      case 'very_high':
        return [
          '• URGENT: Schedule cardiology consultation within 1-2 weeks',
          '• Consider cardiac stress testing and advanced imaging',
          '• Aggressive risk factor modification required',
          '• Daily aspirin therapy (if not contraindicated)',
          '• Statin therapy likely indicated'
        ];
      case 'high':
        return [
          '• Schedule cardiology consultation within 2-4 weeks',
          '• Cardiac risk stratification testing recommended',
          '• Intensive lifestyle modifications',
          '• Consider preventive medications',
          '• Regular cardiac monitoring'
        ];
      case 'moderate':
        return [
          '• Schedule cardiology consultation within 1-2 months',
          '• Basic cardiac screening (ECG, echocardiogram)',
          '• Lifestyle modifications emphasized',
          '• Regular blood pressure and cholesterol monitoring',
          '• Exercise stress test may be appropriate'
        ];
      default:
        return [
          '• Routine cardiac screening as age-appropriate',
          '• Focus on heart-healthy lifestyle',
          '• Regular primary care follow-up',
          '• Monitor blood pressure and cholesterol',
          '• Maintain healthy weight and exercise regularly'
        ];
    }
  }

  /**
   * Analyze need for specialist collaboration
   */
  private analyzeForSpecialistCollaboration(request: AgentRequest): AgentHandoffSuggestion[] {
    const message = request.userMessage.toLowerCase();
    const suggestions: AgentHandoffSuggestion[] = [];

    // Endocrinology for diabetes management
    if (message.includes('diabetes') || message.includes('blood sugar')) {
      suggestions.push({
        targetAgentRole: 'nutritionist', // Using nutritionist as endocrinologist not implemented
        reason: 'Diabetes management requires nutritional expertise for optimal cardiac outcomes',
        urgency: 'medium',
        contextToTransfer: 'Patient with diabetes and cardiac concerns - integrated care needed'
      });
    }

    // Mental health for cardiac psychology
    if (message.includes('anxiety') || message.includes('stress') || message.includes('depression')) {
      suggestions.push({
        targetAgentRole: 'psychiatrist',
        reason: 'Mental health significantly impacts cardiovascular health and recovery',
        urgency: 'medium',
        contextToTransfer: 'Cardiac patient with psychological concerns - integrated care approach'
      });
    }

    // Emergency for acute situations
    if (message.includes('emergency') || message.includes('urgent')) {
      suggestions.push({
        targetAgentRole: 'emergency',
        reason: 'Acute cardiac situation may require emergency medicine expertise',
        urgency: 'high',
        contextToTransfer: 'Potential acute cardiac situation requiring emergency evaluation'
      });
    }

    return suggestions;
  }

  /**
   * Calculate confidence for cardiac consultations
   */
  private calculateCardiacConfidence(request: AgentRequest): number {
    let confidence = 0.9; // High base confidence for cardiology

    const message = request.userMessage.toLowerCase();

    // High confidence for cardiac symptoms
    const cardiacKeywords = [
      'chest pain', 'heart', 'cardiac', 'blood pressure', 'cholesterol',
      'palpitations', 'shortness of breath', 'cardiovascular'
    ];

    if (cardiacKeywords.some(keyword => message.includes(keyword))) {
      confidence = 0.95;
    }

    // Lower confidence for non-cardiac topics
    const nonCardiacKeywords = [
      'skin', 'dermatology', 'rash', 'joint pain', 'headache', 'stomach'
    ];

    if (nonCardiacKeywords.some(keyword => message.includes(keyword))) {
      confidence -= 0.3;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Generate cardiac follow-up actions
   */
  private generateCardiacFollowUp(request: AgentRequest, riskAssessment: CardiacRiskAssessment) {
    const actions = [];

    // Always recommend cardiology follow-up
    actions.push({
      type: 'schedule_appointment' as const,
      description: 'Schedule cardiology consultation for comprehensive cardiac evaluation',
      timeframe: this.getAppointmentTimeframe(riskAssessment.riskLevel),
      priority: riskAssessment.riskLevel === 'very_high' ? 'high' as const : 'medium' as const
    });

    // Risk-specific follow-ups
    if (riskAssessment.riskLevel === 'high' || riskAssessment.riskLevel === 'very_high') {
      actions.push({
        type: 'test_results' as const,
        description: 'Cardiac testing (ECG, echocardiogram, stress test) as recommended',
        timeframe: 'Within 2-4 weeks',
        priority: 'high' as const
      });
    }

    return actions;
  }

  /**
   * Get appointment timeframe based on risk level
   */
  private getAppointmentTimeframe(riskLevel: string): string {
    switch (riskLevel) {
      case 'very_high': return 'Within 1-2 weeks (urgent)';
      case 'high': return 'Within 2-4 weeks';
      case 'moderate': return 'Within 1-2 months';
      default: return 'Routine scheduling (2-3 months)';
    }
  }

  /**
   * Enhanced confidence scoring for cardiac requests
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = super.getConfidenceScore(request);

    // Cardiologist is highly confident for cardiac issues
    const cardiacKeywords = [
      'heart', 'cardiac', 'chest pain', 'blood pressure', 'cholesterol',
      'palpitations', 'cardiovascular', 'coronary', 'arrhythmia'
    ];

    const message = request.userMessage.toLowerCase();
    if (cardiacKeywords.some(keyword => message.includes(keyword))) {
      score += 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Retrieve relevant cardiovascular knowledge using RAG tool
   */
  private async retrieveCardiovascularKnowledge(userMessage: string): Promise<any> {
    try {
      console.log('🔍 Retrieving cardiovascular knowledge...');

      // Use RAG tool to search for relevant information
      const ragResponse = await this.executeTool('rag-tool-001', {
        query: userMessage,
        parameters: {
          maxResults: 3,
          minRelevanceScore: 0.75,
          documentTypes: ['guideline', 'protocol', 'research'],
          specialtyFilter: 'cardiology',
          urgencyLevel: 'medium'
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: 'cardio-knowledge-search',
        agentId: this.id
      });

      if (ragResponse.success && ragResponse.result) {
        console.log(`✅ Retrieved ${ragResponse.result.documents?.length || 0} relevant cardiovascular documents`);
        return {
          documents: ragResponse.result.documents,
          summary: ragResponse.result.summary,
          sources: ragResponse.result.sources,
          averageEvidenceLevel: this.calculateAverageEvidenceLevel(ragResponse.result.documents),
          confidence: ragResponse.confidence
        };
      }

      return null;

    } catch (error) {
      console.error('❌ Failed to retrieve cardiovascular knowledge:', error);
      return null;
    }
  }

  /**
   * Generate enhanced cardiology response using retrieved knowledge
   */
  private async generateEnhancedCardiologyResponse(
    request: AgentRequest,
    riskAssessment: any,
    knowledgeContext: any
  ): Promise<string> {
    let response = `Thank you for your cardiovascular health concern. As a cardiologist, I'll provide you with evidence-based guidance.`;

    // Incorporate knowledge from RAG if available
    if (knowledgeContext && knowledgeContext.documents?.length > 0) {
      response += `\n\nBased on current cardiovascular guidelines and research:\n`;

      knowledgeContext.documents.forEach((doc: any, index: number) => {
        response += `\n${index + 1}. **${doc.title}** (Evidence Level ${doc.evidenceLevel})\n`;
        response += `   ${doc.content.substring(0, 200)}...\n`;
      });

      response += `\n**Sources:** ${knowledgeContext.sources.join(', ')}`;
    }

    // Add cardiovascular assessment
    response += `\n\nFor your specific cardiovascular concern, I recommend:\n`;
    response += `• Comprehensive cardiovascular evaluation\n`;
    response += `• Risk factor assessment and modification\n`;
    response += `• Appropriate cardiac diagnostic testing if indicated\n`;
    response += `• Follow-up with a cardiologist for specialized care\n`;

    // Include risk assessment if available
    if (riskAssessment) {
      response += `\n**Risk Assessment:** ${riskAssessment.riskLevel} cardiovascular risk\n`;
      if (riskAssessment.recommendedActions?.length > 0) {
        response += `**Recommended Actions:**\n${riskAssessment.recommendedActions.map((action: string) => `• ${action}`).join('\n')}\n`;
      }
    }

    response += `\n⚠️ **Important:** This guidance is based on current medical evidence but does not replace in-person cardiovascular evaluation. Please consult with a cardiologist for personalized assessment and treatment.`;

    return response;
  }

  /**
   * Calculate average evidence level from retrieved documents
   */
  private calculateAverageEvidenceLevel(documents: any[]): string {
    if (!documents || documents.length === 0) return 'C';

    const evidenceScores: Record<string, number> = { 'A': 4, 'B': 3, 'C': 2, 'D': 1 };
    const avgScore = documents.reduce((sum, doc) =>
      sum + (evidenceScores[doc.evidenceLevel as keyof typeof evidenceScores] || 2), 0
    ) / documents.length;

    if (avgScore >= 3.5) return 'A';
    if (avgScore >= 2.5) return 'B';
    if (avgScore >= 1.5) return 'C';
    return 'D';
  }
}

interface CardiacRiskAssessment {
  riskLevel: 'low' | 'moderate' | 'high' | 'very_high';
  riskScore: number;
  riskFactors: string[];
  recommendedActions: string[];
}

export default CardiologistAgent;
