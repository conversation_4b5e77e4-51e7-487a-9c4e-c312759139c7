/**
 * ROLE-BASED ACCESS CONTROL (RBAC) SERVICE
 * 
 * This service provides comprehensive role-based access control for the
 * VoiceHealth AI healthcare application with:
 * 
 * 1. Hierarchical role system (patient < provider < admin)
 * 2. Fine-grained permissions for medical data access
 * 3. Emergency access protocols with audit logging
 * 4. Context-aware permission evaluation
 * 5. Real-time permission checking
 * 
 * SECURITY REQUIREMENTS:
 * - All medical data access must be authorized
 * - Emergency access must be logged and justified
 * - Role inheritance with proper security boundaries
 * - Audit trail for all permission checks
 * - Fail-safe defaults (deny by default)
 */

import type {
  User,
  UserRole,
  Permission,
  ResourceType,
  Action,
  RoleDefinition,
  EmergencyAccessRequest,
  EmergencyAccessLog,
  RBACService as IRBACService
} from '../types';

import { ROLE_DEFINITIONS } from '../types';

import { supabase } from '../utils/supabaseClient';
import auditLogger from '../utils/auditLogger';

interface PermissionContext {
  readonly userId?: string;
  readonly resourceOwnerId?: string;
  readonly emergencyOverride?: boolean;
  readonly patientId?: string;
  readonly urgency?: 'routine' | 'urgent' | 'emergency';
  readonly justification?: string;
}

interface EmergencyAccessConfig {
  readonly maxDurationMinutes: number;
  readonly requiredJustificationLength: number;
  readonly autoApprovalRoles: UserRole[];
  readonly requiresApproval: boolean;
}

class RBACService implements IRBACService {
  private readonly emergencyConfig: EmergencyAccessConfig;
  private readonly roleHierarchy: Map<UserRole, UserRole[]>;

  constructor() {
    this.emergencyConfig = {
      maxDurationMinutes: 60, // 1 hour emergency access
      requiredJustificationLength: 20,
      autoApprovalRoles: ['admin', 'emergency_responder'],
      requiresApproval: true
    } as const;

    // Define role hierarchy (higher roles inherit permissions from lower roles)
    this.roleHierarchy = new Map([
      ['patient', []],
      ['healthcare_provider', ['patient']],
      ['admin', ['healthcare_provider', 'patient']],
      ['emergency_responder', []]
    ]);
  }

  /**
   * Get all permissions for a user based on their role and role inheritance
   */
  getUserPermissions(user: User): Permission[] {
    const userRole = user.user_metadata?.role as UserRole || 'patient';
    const roleDefinition = ROLE_DEFINITIONS[userRole];
    
    if (!roleDefinition) {
      console.warn(`Unknown role: ${userRole}, defaulting to patient permissions`);
      return ROLE_DEFINITIONS.patient.permissions;
    }

    let allPermissions: Permission[] = [...roleDefinition.permissions];

    // Add inherited permissions
    if (roleDefinition.inherits_from) {
      for (const inheritedRole of roleDefinition.inherits_from) {
        const inheritedDefinition = ROLE_DEFINITIONS[inheritedRole];
        if (inheritedDefinition) {
          allPermissions = [...allPermissions, ...inheritedDefinition.permissions];
        }
      }
    }

    // Remove duplicates based on resource and action
    const uniquePermissions = allPermissions.filter((permission, index, self) =>
      index === self.findIndex(p => 
        p.resource === permission.resource && p.action === permission.action
      )
    );

    return uniquePermissions;
  }

  /**
   * Check if user has permission for a specific resource and action
   */
  hasPermission(
    user: User, 
    resource: ResourceType, 
    action: Action, 
    context: PermissionContext = {}
  ): boolean {
    try {
      const userRole = user.user_metadata?.role as UserRole || 'patient';
      const permissions = this.getUserPermissions(user);

      // Check for emergency override
      if (context.emergencyOverride && this.checkEmergencyOverride(user)) {
        this.logPermissionCheck(user, resource, action, true, 'emergency_override', context);
        return true;
      }

      // Find matching permission
      const matchingPermission = permissions.find(p => 
        p.resource === resource && p.action === action
      );

      if (!matchingPermission) {
        this.logPermissionCheck(user, resource, action, false, 'no_permission', context);
        return false;
      }

      // Evaluate permission conditions
      const conditionsMet = this.evaluatePermissionConditions(
        matchingPermission, 
        user, 
        context
      );

      this.logPermissionCheck(
        user, 
        resource, 
        action, 
        conditionsMet, 
        conditionsMet ? 'granted' : 'conditions_failed', 
        context
      );

      return conditionsMet;
    } catch (error) {
      console.error('Error checking permission:', error);
      this.logPermissionCheck(user, resource, action, false, 'error', context);
      return false; // Fail-safe: deny by default
    }
  }

  /**
   * Evaluate permission conditions
   */
  private evaluatePermissionConditions(
    permission: Permission,
    user: User,
    context: PermissionContext
  ): boolean {
    if (!permission.conditions || permission.conditions.length === 0) {
      return true; // No conditions means permission is granted
    }

    return permission.conditions.every(condition => {
      switch (condition.field) {
        case 'user_id':
          if (condition.operator === 'equals' && condition.value === 'self') {
            return context.userId === user.id || context.resourceOwnerId === user.id;
          }
          return this.evaluateCondition(context.userId, condition.operator, condition.value);

        case 'patient_id':
          if (condition.operator === 'equals' && condition.value === 'self') {
            return context.patientId === user.id;
          }
          return this.evaluateCondition(context.patientId, condition.operator, condition.value);

        case 'urgency':
          return this.evaluateCondition(context.urgency, condition.operator, condition.value);

        default:
          console.warn(`Unknown condition field: ${condition.field}`);
          return false; // Fail-safe: unknown conditions fail
      }
    });
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(
    fieldValue: unknown,
    operator: string,
    expectedValue: unknown
  ): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue;
      case 'not_equals':
        return fieldValue !== expectedValue;
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
      case 'not_in':
        return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
      case 'greater_than':
        return typeof fieldValue === 'number' && typeof expectedValue === 'number' && 
               fieldValue > expectedValue;
      case 'less_than':
        return typeof fieldValue === 'number' && typeof expectedValue === 'number' && 
               fieldValue < expectedValue;
      default:
        console.warn(`Unknown condition operator: ${operator}`);
        return false;
    }
  }

  /**
   * Check if user can use emergency override
   */
  checkEmergencyOverride(user: User): boolean {
    const userRole = user.user_metadata?.role as UserRole || 'patient';
    const roleDefinition = ROLE_DEFINITIONS[userRole];
    return roleDefinition?.emergency_override === true;
  }

  /**
   * Request emergency access to patient data
   */
  async requestEmergencyAccess(
    patientId: string,
    reason: string,
    urgency: 'high' | 'critical'
  ): Promise<EmergencyAccessRequest> {
    try {
      // Validate justification length
      if (reason.length < this.emergencyConfig.requiredJustificationLength) {
        throw new Error(`Justification must be at least ${this.emergencyConfig.requiredJustificationLength} characters`);
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const requestId = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + this.emergencyConfig.maxDurationMinutes);

      const userRole = user.user_metadata?.role as UserRole || 'patient';
      const autoApprove = this.emergencyConfig.autoApprovalRoles.includes(userRole);

      const emergencyRequest: EmergencyAccessRequest = {
        id: requestId,
        requester_id: user.id,
        patient_id: patientId,
        reason,
        urgency,
        requested_at: new Date().toISOString(),
        ...(autoApprove ? { approved_at: new Date().toISOString() } : {}),
        ...(autoApprove ? { approved_by: user.id } : {}),
        expires_at: expiresAt.toISOString(),
        status: autoApprove ? 'approved' : 'pending'
      };

      // Store emergency access request
      const { error } = await supabase
        .from('emergency_access_requests')
        .insert({
          id: emergencyRequest.id,
          requester_id: emergencyRequest.requester_id,
          patient_id: emergencyRequest.patient_id,
          reason: emergencyRequest.reason,
          urgency: emergencyRequest.urgency,
          requested_at: emergencyRequest.requested_at,
          approved_at: emergencyRequest.approved_at,
          approved_by: emergencyRequest.approved_by,
          expires_at: emergencyRequest.expires_at,
          status: emergencyRequest.status
        });

      if (error) {
        throw new Error(`Failed to create emergency access request: ${error.message}`);
      }

      // Log emergency access request
      await auditLogger.logEmergencyAccess(
        'emergency_access_request',
        requestId,
        `Emergency access requested for patient ${patientId}`,
        {
          patient_id: patientId,
          urgency,
          reason,
          auto_approved: autoApprove,
          requester_role: userRole
        }
      );

      return emergencyRequest;
    } catch (error) {
      console.error('Failed to request emergency access:', error);
      throw error;
    }
  }

  /**
   * Approve emergency access request
   */
  async approveEmergencyAccess(requestId: string, approverId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('emergency_access_requests')
        .update({
          approved_at: new Date().toISOString(),
          approved_by: approverId,
          status: 'approved'
        })
        .eq('id', requestId)
        .eq('status', 'pending')
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to approve emergency access: ${error.message}`);
      }

      if (!data) {
        throw new Error('Emergency access request not found or already processed');
      }

      // Log emergency access approval
      await auditLogger.logEmergencyAccess(
        'emergency_access_approval',
        requestId,
        `Emergency access approved for patient ${data.patient_id}`,
        {
          patient_id: data.patient_id,
          approved_by: approverId,
          original_requester: data.requester_id
        }
      );
    } catch (error) {
      console.error('Failed to approve emergency access:', error);
      throw error;
    }
  }

  /**
   * Log emergency access usage
   */
  async logEmergencyAccess(
    accessRequestId: string,
    resourcesAccessed: ResourceType[],
    actionsPerformed: Action[],
    justification: string
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get the original emergency access request
      const { data: request, error: requestError } = await supabase
        .from('emergency_access_requests')
        .select('*')
        .eq('id', accessRequestId)
        .single();

      if (requestError || !request) {
        throw new Error('Emergency access request not found');
      }

      const logEntry: Omit<EmergencyAccessLog, 'id' | 'created_at'> = {
        access_request_id: accessRequestId,
        user_id: user.id,
        patient_id: request.patient_id,
        resources_accessed: resourcesAccessed,
        actions_performed: actionsPerformed,
        access_duration_minutes: Math.floor(
          (Date.now() - new Date(request.approved_at || request.requested_at).getTime()) / 60000
        ),
        justification
      };

      // Store emergency access log
      const { error } = await supabase
        .from('emergency_access_logs')
        .insert({
          ...logEntry,
          id: crypto.randomUUID(),
          created_at: new Date().toISOString()
        });

      if (error) {
        throw new Error(`Failed to log emergency access: ${error.message}`);
      }

      // Also log in audit system
      await auditLogger.logEmergencyAccess(
        'emergency_data_access',
        request.patient_id,
        justification,
        {
          access_request_id: accessRequestId,
          resources_accessed: resourcesAccessed,
          actions_performed: actionsPerformed,
          access_duration_minutes: logEntry.access_duration_minutes
        }
      );
    } catch (error) {
      console.error('Failed to log emergency access:', error);
      throw error;
    }
  }

  /**
   * Log permission check for audit purposes
   */
  private async logPermissionCheck(
    user: User,
    resource: ResourceType,
    action: Action,
    granted: boolean,
    reason: string,
    context: PermissionContext
  ): Promise<void> {
    try {
      await auditLogger.logMedicalDataAccess(
        'permission_check',
        'rbac_permission',
        `${resource}_${action}`,
        {
          user_id: user.id,
          user_role: user.user_metadata?.role,
          resource,
          action,
          granted,
          reason,
          context: {
            emergency_override: context.emergencyOverride,
            urgency: context.urgency,
            patient_id: context.patientId
          }
        }
      );
    } catch (error) {
      console.error('Failed to log permission check:', error);
    }
  }
}

// Export singleton instance
const rbacService = new RBACService();
export default rbacService;
