import { supabase } from './supabaseClient';
import sendPulseService from './sendPulseService';

const consultationService = {
  // Create a new consultation session
  createSession: async (patientId, agentId, sessionData = {}) => {
    try {
      const { data, error } = await supabase
        .from('consultation_sessions')
        .insert({
          patient_id: patientId,
          primary_agent_id: agentId,
          session_title: sessionData?.title || 'General Consultation',
          status: 'scheduled',
          current_phase: 'ready',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Send consultation confirmation email
      try {
        // Get user profile for email
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('email, full_name')
          .eq('id', patientId)
          .single();

        // Get agent details
        const { data: agentData } = await supabase
          .from('ai_agents')
          .select('name, specialty')
          .eq('id', agentId)
          .single();

        if (userProfile?.email) {
          await sendPulseService.sendConsultationConfirmation(
            userProfile.email,
            userProfile.full_name || userProfile.email.split('@')[0],
            {
              id: data.id,
              title: data.session_title,
              agentName: agentData?.name,
              specialty: agentData?.specialty,
              status: data.status
            }
          );
        }
      } catch (emailError) {
        console.warn('Failed to send consultation confirmation email:', emailError);
        // Don't fail the session creation if email fails
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to create consultation session' };
    }
  },

  // Get user's consultation sessions
  getUserSessions: async (userId) => {
    try {
      const { data, error } = await supabase
        .from('consultation_sessions')
        .select(`
          *,
          ai_agents!consultation_sessions_primary_agent_id_fkey (
            id,
            name,
            specialty,
            avatar_url
          )
        `)
        .eq('patient_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to load consultation sessions' };
    }
  },

  // Update session status
  updateSession: async (sessionId, updates) => {
    try {
      const { data, error } = await supabase
        .from('consultation_sessions')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to update session' };
    }
  },

  // Add conversation message
  addMessage: async (sessionId, messageData) => {
    try {
      const { data, error } = await supabase
        .from('conversation_messages')
        .insert({
          session_id: sessionId,
          speaker_type: messageData.speaker_type,
          speaker_id: messageData.speaker_id,
          speaker_name: messageData.speaker_name,
          content: messageData.content,
          confidence_score: messageData.confidence_score || 0.95,
          sequence_number: messageData.sequence_number,
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to add message' };
    }
  },

  // Get session conversation
  getSessionConversation: async (sessionId) => {
    try {
      const { data, error } = await supabase
        .from('conversation_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('sequence_number', { ascending: true });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to load conversation' };
    }
  },

  // Get available AI agents
  getAvailableAgents: async () => {
    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .eq('is_active', true)
        .order('specialty', { ascending: true });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to load AI agents' };
    }
  },

  // Add session recommendation
  addRecommendation: async (sessionId, recommendationData) => {
    try {
      const { data, error } = await supabase
        .from('session_recommendations')
        .insert({
          session_id: sessionId,
          recommending_agent_id: recommendationData.agent_id,
          title: recommendationData.title,
          content: recommendationData.content,
          priority: recommendationData.priority || 'Medium',
          category: recommendationData.category,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to add recommendation' };
    }
  },

  // Get session recommendations
  getSessionRecommendations: async (sessionId) => {
    try {
      const { data, error } = await supabase
        .from('session_recommendations')
        .select(`
          *,
          ai_agents!session_recommendations_recommending_agent_id_fkey (
            name,
            specialty
          )
        `)
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to load recommendations' };
    }
  },

  // Complete consultation session
  completeSession: async (sessionId, completionData = {}) => {
    try {
      const { data, error } = await supabase
        .from('consultation_sessions')
        .update({
          status: 'completed',
          current_phase: 'completed',
          completed_at: new Date().toISOString(),
          session_summary: completionData?.summary || null,
          ...completionData,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .select(`
          *,
          ai_agents!consultation_sessions_primary_agent_id_fkey (
            name,
            specialty
          )
        `)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Send completion email with summary and recommendations
      try {
        // Get user profile
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('email, full_name')
          .eq('id', data.patient_id)
          .single();

        // Get session recommendations
        const { data: recommendations } = await supabase
          .from('session_recommendations')
          .select('*')
          .eq('session_id', sessionId)
          .order('created_at', { ascending: false });

        if (userProfile?.email) {
          await sendPulseService.sendConsultationSummary(
            userProfile.email,
            userProfile.full_name || userProfile.email.split('@')[0],
            {
              id: data.id,
              title: data.session_title,
              date: data.completed_at,
              duration: completionData?.duration,
              agentName: data.ai_agents?.name
            },
            recommendations || []
          );
        }
      } catch (emailError) {
        console.warn('Failed to send consultation summary email:', emailError);
        // Don't fail the completion if email fails
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to complete session' };
    }
  }
};

export default consultationService;