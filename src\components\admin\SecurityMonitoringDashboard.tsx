/**
 * SECURITY MONITORING DASHBOARD
 * 
 * Real-time security monitoring dashboard for administrators to track
 * rate limiting, DDoS attacks, emergency bypasses, and security events
 * across the medical application.
 * 
 * MONITORING FEATURES:
 * - Real-time rate limiting statistics
 * - DDoS attack detection and alerts
 * - Emergency bypass tracking
 * - Security event timeline
 * - Geographic threat analysis
 * - User behavior analytics
 * 
 * MEDICAL COMPLIANCE:
 * - HIPAA-compliant logging (no PHI in security logs)
 * - Audit trail for all security events
 * - Emergency access monitoring
 * - Role-based security insights
 */

import React, { useState, useEffect } from 'react';
import { useRBAC } from '../../hooks/useRBAC';
import clientRateLimitManager from '../../utils/clientRateLimiting';

interface SecurityMetrics {
  readonly rateLimitHits: number;
  readonly ddosAttempts: number;
  readonly emergencyBypasses: number;
  readonly blockedIPs: number;
  readonly totalRequests: number;
  readonly averageResponseTime: number;
}

interface SecurityEvent {
  readonly id: string;
  readonly timestamp: string;
  readonly eventType: string;
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly details: Record<string, unknown>;
  readonly clientIp?: string;
  readonly userId?: string;
  readonly userRole?: string;
}

interface RateLimitStats {
  readonly operationType: string;
  readonly totalRequests: number;
  readonly blockedRequests: number;
  readonly averageWaitTime: number;
  readonly peakRequestsPerMinute: number;
}

export const SecurityMonitoringDashboard: React.FC = () => {
  const { hasPermission } = useRBAC();
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    rateLimitHits: 0,
    ddosAttempts: 0,
    emergencyBypasses: 0,
    blockedIPs: 0,
    totalRequests: 0,
    averageResponseTime: 0
  });

  const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
  const [rateLimitStats, setRateLimitStats] = useState<RateLimitStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');

  // Check permissions
  const canViewSecurity = hasPermission('security_logs', 'read');

  useEffect(() => {
    if (!canViewSecurity) return;

    loadSecurityData();
    
    if (autoRefresh) {
      const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
    return undefined;
  }, [canViewSecurity, autoRefresh, selectedTimeRange]);

  /**
   * Load security monitoring data
   */
  const loadSecurityData = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Load security metrics
      const metricsResponse = await fetch(`/api/admin/security/metrics?timeRange=${selectedTimeRange}`);
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData);
      }

      // Load recent security events
      const eventsResponse = await fetch(`/api/admin/security/events?limit=50&timeRange=${selectedTimeRange}`);
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setRecentEvents(eventsData.events || []);
      }

      // Load rate limiting statistics
      const rateLimitResponse = await fetch(`/api/admin/security/rate-limits?timeRange=${selectedTimeRange}`);
      if (rateLimitResponse.ok) {
        const rateLimitData = await rateLimitResponse.json();
        setRateLimitStats(rateLimitData.stats || []);
      }

    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get severity color class
   */
  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  /**
   * Format event type for display
   */
  const formatEventType = (eventType: string): string => {
    return eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  /**
   * Get client-side rate limit status
   */
  const clientRateLimitStatus = clientRateLimitManager.getRateLimitStatus();
  const queueStats = clientRateLimitManager.getQueueStats();

  if (!canViewSecurity) {
    return (
      <div className="p-8 bg-red-50 border border-red-200 rounded">
        <h2 className="text-red-800 font-bold">Access Denied</h2>
        <p className="text-red-700 mt-2">You don't have permission to view security monitoring data.</p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Monitoring</h1>
          <p className="text-gray-600">Real-time security and rate limiting dashboard</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Time Range Selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>

          {/* Auto Refresh Toggle */}
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Auto Refresh</span>
          </label>

          {/* Manual Refresh Button */}
          <button
            onClick={loadSecurityData}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Security Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">Rate Limit Hits</div>
          <div className="text-2xl font-bold text-orange-600">{metrics.rateLimitHits.toLocaleString()}</div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">DDoS Attempts</div>
          <div className="text-2xl font-bold text-red-600">{metrics.ddosAttempts.toLocaleString()}</div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">Emergency Bypasses</div>
          <div className="text-2xl font-bold text-yellow-600">{metrics.emergencyBypasses.toLocaleString()}</div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">Blocked IPs</div>
          <div className="text-2xl font-bold text-red-600">{metrics.blockedIPs.toLocaleString()}</div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">Total Requests</div>
          <div className="text-2xl font-bold text-blue-600">{metrics.totalRequests.toLocaleString()}</div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="text-sm font-medium text-gray-500">Avg Response Time</div>
          <div className="text-2xl font-bold text-green-600">{metrics.averageResponseTime}ms</div>
        </div>
      </div>

      {/* Client-Side Rate Limiting Status */}
      <div className="bg-white rounded-lg shadow border">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Client-Side Rate Limiting</h2>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm font-medium text-gray-500">Queue Length</div>
              <div className="text-xl font-bold text-blue-600">{queueStats.queueLength}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm font-medium text-gray-500">Active Requests</div>
              <div className="text-xl font-bold text-green-600">{queueStats.activeRequests}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm font-medium text-gray-500">Operation Types</div>
              <div className="text-xl font-bold text-purple-600">{Object.keys(queueStats.byPriority).length}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            {clientRateLimitStatus.map((status) => (
              <div key={status.operationType} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="font-medium">{status.operationType.replace('_', ' ')}</span>
                <div className="flex items-center space-x-4">
                  <span className={`text-sm ${status.blocked ? 'text-red-600' : 'text-green-600'}`}>
                    {status.remaining} remaining
                  </span>
                  <span className="text-xs text-gray-500">
                    Resets: {new Date(status.resetTime).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Rate Limiting Statistics */}
      <div className="bg-white rounded-lg shadow border">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Rate Limiting Statistics</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Operation Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Blocked Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Block Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Peak RPM
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {rateLimitStats.map((stat) => (
                <tr key={stat.operationType}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatEventType(stat.operationType)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {stat.totalRequests.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {stat.blockedRequests.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {stat.totalRequests > 0 ? 
                      ((stat.blockedRequests / stat.totalRequests) * 100).toFixed(1) : 0}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {stat.peakRequestsPerMinute.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Security Events */}
      <div className="bg-white rounded-lg shadow border">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Security Events</h2>
        </div>
        <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
          {recentEvents.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              No security events in the selected time range
            </div>
          ) : (
            recentEvents.map((event) => (
              <div key={event.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getSeverityColor(event.severity)}`}>
                        {event.severity.toUpperCase()}
                      </span>
                      <span className="font-medium text-gray-900">
                        {formatEventType(event.eventType)}
                      </span>
                    </div>
                    <div className="mt-1 text-sm text-gray-600">
                      {event.clientIp && <span>IP: {event.clientIp} • </span>}
                      {event.userRole && <span>Role: {event.userRole} • </span>}
                      <span>{new Date(event.timestamp).toLocaleString()}</span>
                    </div>
                    {Object.keys(event.details).length > 0 && (
                      <div className="mt-2 text-xs text-gray-500">
                        <details>
                          <summary className="cursor-pointer">View Details</summary>
                          <pre className="mt-1 bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                            {JSON.stringify(event.details, null, 2)}
                          </pre>
                        </details>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default SecurityMonitoringDashboard;
