/**
 * MEDICAL IMAGES API ENDPOINT
 * 
 * Provides secure API endpoints for medical image metadata CRUD operations
 * with HIPAA-compliant handling and comprehensive audit logging.
 * 
 * FEATURES:
 * - Medical image metadata management
 * - Secure image upload coordination
 * - Analysis status tracking
 * - HIPAA-compliant access control
 * - Comprehensive audit logging
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, param, query, validationResult } = require('express-validator');
const authMiddleware = require('./middleware/authMiddleware');
const rbacMiddleware = require('./middleware/rbacMiddleware');
const auditLogger = require('./utils/auditLogger');
const { createClient } = require('@supabase/supabase-js');

const router = express.Router();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Rate limiting configuration
const imageRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: { error: 'Rate limit exceeded for medical images' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.user?.id || req.ip
});

// Validation middleware
const validateImageMetadata = [
  body('sessionId').isUUID().withMessage('Valid session ID required'),
  body('storagePath').isString().isLength({ min: 1, max: 500 }).withMessage('Storage path required'),
  body('originalFilename').isString().isLength({ min: 1, max: 255 }).withMessage('Original filename required'),
  body('fileSize').isInt({ min: 1, max: 10485760 }).withMessage('File size must be 1 byte to 10MB'),
  body('mimeType').isIn(['image/jpeg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff'])
    .withMessage('Valid image MIME type required'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }
    next();
  }
];

/**
 * POST /api/medical-images
 * Create medical image metadata record
 */
router.post('/',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'create'),
  validateImageMetadata,
  async (req, res) => {
    const startTime = Date.now();
    const {
      sessionId,
      storagePath,
      originalFilename,
      fileSize,
      mimeType
    } = req.body;

    try {
      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        await auditLogger.logSecurityEvent('unauthorized_image_metadata_creation', false, {
          user_id: req.user.id,
          session_id: sessionId,
          ip_address: req.ip
        });

        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Create image metadata record
      const { data: imageRecord, error } = await supabase
        .from('medical_images')
        .insert({
          session_id: sessionId,
          user_id: req.user.id,
          storage_path: storagePath,
          original_filename: originalFilename,
          file_size: fileSize,
          mime_type: mimeType,
          analysis_status: 'pending'
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Audit log the metadata creation
      await auditLogger.logDataAccess('medical_image_metadata', sessionId, true, {
        operation: 'create_image_metadata',
        session_id: sessionId,
        image_id: imageRecord.id,
        user_id: req.user.id,
        file_size: fileSize,
        mime_type: mimeType,
        storage_path: storagePath,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          id: imageRecord.id,
          sessionId: imageRecord.session_id,
          storagePath: imageRecord.storage_path,
          originalFilename: imageRecord.original_filename,
          fileSize: imageRecord.file_size,
          mimeType: imageRecord.mime_type,
          analysisStatus: imageRecord.analysis_status,
          uploadTimestamp: imageRecord.upload_timestamp,
          createdAt: imageRecord.created_at
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Medical image metadata creation error:', error);

      await auditLogger.logDataAccess('medical_image_metadata', sessionId, false, {
        operation: 'create_image_metadata',
        session_id: sessionId,
        user_id: req.user.id,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to create image metadata'
      });
    }
  }
);

/**
 * GET /api/medical-images/session/:sessionId
 * Get all medical images for a session
 */
router.get('/session/:sessionId',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'read'),
  [param('sessionId').isUUID().withMessage('Valid session ID required')],
  async (req, res) => {
    try {
      const { sessionId } = req.params;

      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Get all images for the session
      const { data: images, error } = await supabase
        .from('medical_images')
        .select('*')
        .eq('session_id', sessionId)
        .order('upload_timestamp', { ascending: false });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: images || []
      });

    } catch (error) {
      console.error('❌ Get session images error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve session images'
      });
    }
  }
);

/**
 * GET /api/medical-images/:imageId
 * Get specific medical image metadata
 */
router.get('/:imageId',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'read'),
  [param('imageId').isUUID().withMessage('Valid image ID required')],
  async (req, res) => {
    try {
      const { imageId } = req.params;

      // Get image with session verification
      const { data: image, error } = await supabase
        .from('medical_images')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', imageId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (error || !image) {
        return res.status(403).json({
          success: false,
          error: 'Image not found or access denied'
        });
      }

      // Audit log the access
      await auditLogger.logDataAccess('medical_image_access', image.session_id, true, {
        operation: 'access_image_metadata',
        image_id: imageId,
        session_id: image.session_id,
        user_id: req.user.id
      });

      res.json({
        success: true,
        data: image
      });

    } catch (error) {
      console.error('❌ Get image metadata error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve image metadata'
      });
    }
  }
);

/**
 * PUT /api/medical-images/:imageId/analysis-status
 * Update image analysis status
 */
router.put('/:imageId/analysis-status',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'update'),
  [
    param('imageId').isUUID().withMessage('Valid image ID required'),
    body('analysisStatus').isIn(['pending', 'analyzing', 'completed', 'failed'])
      .withMessage('Valid analysis status required'),
    body('analysisResults').optional().isObject().withMessage('Analysis results must be an object'),
    body('agentId').optional().isString().isLength({ max: 100 }).withMessage('Agent ID must be max 100 chars')
  ],
  async (req, res) => {
    const startTime = Date.now();
    const { imageId } = req.params;
    const { analysisStatus, analysisResults, agentId } = req.body;

    try {
      // Verify image ownership
      const { data: image, error: imageError } = await supabase
        .from('medical_images')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', imageId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (imageError || !image) {
        return res.status(403).json({
          success: false,
          error: 'Image not found or access denied'
        });
      }

      // Update analysis status
      const updateData = {
        analysis_status: analysisStatus,
        updated_at: new Date().toISOString()
      };

      if (analysisResults) {
        updateData.analysis_results = analysisResults;
      }

      if (agentId) {
        updateData.agent_id = agentId;
      }

      const { data: updatedImage, error } = await supabase
        .from('medical_images')
        .update(updateData)
        .eq('id', imageId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Audit log the status update
      await auditLogger.logDataAccess('medical_image_analysis_update', image.session_id, true, {
        operation: 'update_analysis_status',
        image_id: imageId,
        session_id: image.session_id,
        user_id: req.user.id,
        analysis_status: analysisStatus,
        agent_id: agentId,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          id: updatedImage.id,
          analysisStatus: updatedImage.analysis_status,
          analysisResults: updatedImage.analysis_results,
          agentId: updatedImage.agent_id,
          updatedAt: updatedImage.updated_at
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Update analysis status error:', error);

      await auditLogger.logDataAccess('medical_image_analysis_update', 'unknown', false, {
        operation: 'update_analysis_status',
        image_id: imageId,
        user_id: req.user.id,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to update analysis status'
      });
    }
  }
);

/**
 * DELETE /api/medical-images/:imageId
 * Delete medical image metadata (soft delete)
 */
router.delete('/:imageId',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'delete'),
  [param('imageId').isUUID().withMessage('Valid image ID required')],
  async (req, res) => {
    const startTime = Date.now();
    const { imageId } = req.params;

    try {
      // Verify image ownership
      const { data: image, error: imageError } = await supabase
        .from('medical_images')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', imageId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (imageError || !image) {
        return res.status(403).json({
          success: false,
          error: 'Image not found or access denied'
        });
      }

      // Soft delete by updating status (for audit trail)
      const { error } = await supabase
        .from('medical_images')
        .update({
          analysis_status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('id', imageId);

      if (error) {
        throw error;
      }

      // Audit log the deletion
      await auditLogger.logDataAccess('medical_image_deletion', image.session_id, true, {
        operation: 'delete_image_metadata',
        image_id: imageId,
        session_id: image.session_id,
        user_id: req.user.id,
        storage_path: image.storage_path,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          imageId,
          deletedAt: new Date().toISOString()
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Delete image metadata error:', error);

      await auditLogger.logDataAccess('medical_image_deletion', 'unknown', false, {
        operation: 'delete_image_metadata',
        image_id: imageId,
        user_id: req.user.id,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to delete image metadata'
      });
    }
  }
);

/**
 * GET /api/medical-images/user/summary
 * Get user's medical images summary
 */
router.get('/user/summary',
  imageRateLimit,
  authMiddleware,
  rbacMiddleware('medical_images', 'read'),
  async (req, res) => {
    try {
      // Get user's image statistics
      const { data: stats, error } = await supabase
        .rpc('get_user_image_stats', { p_user_id: req.user.id });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: stats || {
          totalImages: 0,
          pendingAnalysis: 0,
          completedAnalysis: 0,
          failedAnalysis: 0,
          totalSessions: 0
        }
      });

    } catch (error) {
      console.error('❌ Get user image summary error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve image summary'
      });
    }
  }
);

module.exports = router;
