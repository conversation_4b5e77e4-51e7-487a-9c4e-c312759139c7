/**
 * SECURE FILE MANAGER COMPONENT
 * 
 * HIPAA-compliant file management interface with secure download,
 * access control, and audit logging capabilities.
 */

import React, { useState, useEffect } from 'react';
import { secureStorageService } from '../services/SecureStorageService';

const SecureFileManager = ({ 
  bucketName = 'medical-records',
  userId,
  userRole = 'patient',
  emergencyAccess = false
}) => {
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFile, setSelectedFile] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState({});
  const [error, setError] = useState('');

  // Load files on component mount
  useEffect(() => {
    if (userId) {
      loadFiles();
    }
  }, [userId, bucketName]);

  const loadFiles = async () => {
    try {
      setIsLoading(true);
      setError('');

      const result = await secureStorageService.secureList(bucketName, userId);
      
      if (result.success) {
        setFiles(result.files || []);
      } else {
        setError(result.error || 'Failed to load files');
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      setError('Failed to load files');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (file) => {
    try {
      setDownloadProgress(prev => ({ ...prev, [file.name]: 0 }));
      
      // Simulate download progress
      const progressInterval = setInterval(() => {
        setDownloadProgress(prev => ({
          ...prev,
          [file.name]: Math.min((prev[file.name] || 0) + 20, 90)
        }));
      }, 200);

      const result = await secureStorageService.secureDownload(
        bucketName,
        file.name,
        userId,
        emergencyAccess
      );

      clearInterval(progressInterval);
      setDownloadProgress(prev => ({ ...prev, [file.name]: 100 }));

      if (result.success) {
        // Create download link
        const url = URL.createObjectURL(result.fileData);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.metadata?.originalName || file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Clear progress after delay
        setTimeout(() => {
          setDownloadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[file.name];
            return newProgress;
          });
        }, 2000);
      } else {
        setError(`Download failed: ${result.error}`);
        setDownloadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[file.name];
          return newProgress;
        });
      }
    } catch (error) {
      console.error('Download error:', error);
      setError(`Download failed: ${error.message}`);
    }
  };

  const handleDelete = async (file) => {
    if (!window.confirm(`Are you sure you want to delete "${file.metadata?.originalName || file.name}"?`)) {
      return;
    }

    try {
      const reason = prompt('Please provide a reason for deletion:');
      if (!reason) return;

      const result = await secureStorageService.secureDelete(
        bucketName,
        file.name,
        userId,
        reason
      );

      if (result.success) {
        setFiles(prev => prev.filter(f => f.name !== file.name));
        alert('File deleted successfully');
      } else {
        setError(`Delete failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      setError(`Delete failed: ${error.message}`);
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-800">
          🔐 Secure File Manager
          {emergencyAccess && (
            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
              EMERGENCY ACCESS
            </span>
          )}
        </h2>
        <button
          onClick={loadFiles}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          🔄 Refresh
        </button>
      </div>

      <div className="mb-4 text-sm text-gray-600">
        <div>Bucket: {bucketName}</div>
        <div>User: {userId} ({userRole})</div>
        <div>Files: {files.length}</div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-800">
          ❌ {error}
        </div>
      )}

      {files.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-4">📁</div>
          <div>No files found in this bucket</div>
        </div>
      ) : (
        <div className="space-y-3">
          {files.map((file) => (
            <div key={file.name} className="border rounded-lg p-4 hover:bg-gray-50">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium text-gray-800">
                    {file.metadata?.originalName || file.name}
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Size: {formatFileSize(file.metadata?.size)}</div>
                    <div>Type: {file.metadata?.content_type || 'Unknown'}</div>
                    <div>Uploaded: {formatDate(file.created_at)}</div>
                    {file.metadata?.encrypted && (
                      <div className="text-green-600">🔐 Encrypted</div>
                    )}
                    {file.metadata?.emergencyMode && (
                      <div className="text-red-600">🚨 Emergency Upload</div>
                    )}
                  </div>
                </div>

                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleDownload(file)}
                    disabled={downloadProgress[file.name] !== undefined}
                    className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors disabled:bg-gray-400"
                  >
                    {downloadProgress[file.name] !== undefined ? (
                      `${downloadProgress[file.name]}%`
                    ) : (
                      '⬇️ Download'
                    )}
                  </button>

                  {(userRole === 'admin' || userRole === 'provider') && (
                    <button
                      onClick={() => handleDelete(file)}
                      className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
                    >
                      🗑️ Delete
                    </button>
                  )}

                  <button
                    onClick={() => setSelectedFile(selectedFile === file ? null : file)}
                    className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                  >
                    ℹ️ Info
                  </button>
                </div>
              </div>

              {/* Download Progress */}
              {downloadProgress[file.name] !== undefined && (
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>Downloading...</span>
                    <span>{downloadProgress[file.name]}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-green-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${downloadProgress[file.name]}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* File Details */}
              {selectedFile === file && (
                <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded text-sm">
                  <div className="font-medium text-gray-800 mb-2">File Details</div>
                  <div className="space-y-1 text-gray-600">
                    <div>ID: {file.id}</div>
                    <div>Path: {file.name}</div>
                    <div>Bucket: {file.bucket_id}</div>
                    <div>Created: {formatDate(file.created_at)}</div>
                    <div>Updated: {formatDate(file.updated_at)}</div>
                    {file.metadata && (
                      <div>
                        <div className="font-medium mt-2 mb-1">Metadata:</div>
                        <pre className="text-xs bg-white p-2 rounded border overflow-auto">
                          {JSON.stringify(file.metadata, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Security Notice */}
      <div className="mt-6 p-3 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600">
        <div className="font-medium mb-1">🛡️ Security Features Active:</div>
        <ul className="space-y-1">
          <li>• End-to-end encryption (AES-256-GCM)</li>
          <li>• Role-based access control</li>
          <li>• Comprehensive audit logging</li>
          <li>• File integrity verification</li>
          <li>• HIPAA compliance monitoring</li>
        </ul>
      </div>
    </div>
  );
};

export default SecureFileManager;
