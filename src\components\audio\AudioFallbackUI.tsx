/**
 * AUDIO FALLBACK UI COMPONENT
 * 
 * Provides alternative consultation interfaces when audio processing fails:
 * - Text-based consultation interface
 * - Emergency contact options
 * - Recovery guidance for patients
 * - Seamless transition back to audio when recovered
 */

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ir<PERSON>, Phone, <PERSON>ert<PERSON>riangle, <PERSON>c, <PERSON>c<PERSON><PERSON>, Refresh<PERSON><PERSON>, CheckCircle } from 'lucide-react';
// @ts-ignore - Button component from JSX file
import ButtonComponent from '../ui/Button.jsx';

// Type the Button component properly
const Button = ButtonComponent as any;

interface AudioFallbackUIProps {
  mode: 'text_only' | 'emergency_mode' | 'recovery_mode';
  error?: {
    message: string;
    code?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
  };
  sessionId?: string;
  patientId?: string;
  onRetryAudio?: () => void;
  onEmergencyContact?: () => void;
  onContinueTextOnly?: () => void;
  onRecoveryComplete?: () => void;
  isRecovering?: boolean;
  recoveryProgress?: number;
  children?: React.ReactNode;
}

const AudioFallbackUI: React.FC<AudioFallbackUIProps> = ({
  mode,
  error,
  sessionId,
  patientId,
  onRetryAudio,
  onEmergencyContact,
  onContinueTextOnly,
  onRecoveryComplete,
  isRecovering = false,
  recoveryProgress = 0,
  children
}) => {
  const [showRecoverySteps, setShowRecoverySteps] = useState(false);
  const [userFeedback, setUserFeedback] = useState('');
  const [hasSubmittedFeedback, setHasSubmittedFeedback] = useState(false);

  useEffect(() => {
    // Auto-hide recovery steps after successful recovery
    if (mode === 'recovery_mode' && recoveryProgress === 100) {
      const timer = setTimeout(() => {
        onRecoveryComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [mode, recoveryProgress, onRecoveryComplete]);

  const handleSubmitFeedback = async () => {
    if (!userFeedback.trim()) return;

    try {
      // Log user feedback for audio issues
      const { default: auditLogger } = await import('../../utils/auditLogger');
      
      await auditLogger.logSecurityEvent('audio_user_feedback', 'low', {
        event_type: 'audio_fallback_feedback',
        feedback: userFeedback,
        error_code: error?.code,
        session_id: sessionId,
        patient_id: patientId,
        fallback_mode: mode,
        timestamp: new Date().toISOString()
      });

      setHasSubmittedFeedback(true);
      setUserFeedback('');
    } catch (logError) {
      console.error('Failed to submit feedback:', logError);
    }
  };

  const renderEmergencyMode = () => (
    <div className="bg-red-50 border-l-4 border-red-500 rounded-lg p-6 mb-6">
      <div className="flex items-center space-x-3 mb-4">
        <AlertTriangle className="w-8 h-8 text-red-600" />
        <div>
          <h2 className="text-xl font-semibold text-red-900">
            Critical Audio Error - Emergency Mode
          </h2>
          <p className="text-sm text-red-700">
            Audio consultation is temporarily unavailable due to a critical error
          </p>
        </div>
      </div>

      <div className="bg-red-100 border border-red-200 rounded-lg p-4 mb-4">
        <h3 className="font-semibold text-red-800 mb-2">Immediate Actions Available:</h3>
        <ul className="text-sm text-red-700 space-y-1">
          <li>• Contact emergency services if this is a medical emergency</li>
          <li>• Continue consultation via text messaging</li>
          <li>• Schedule a callback with healthcare provider</li>
          <li>• Access emergency health resources</li>
        </ul>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <Button
          onClick={onEmergencyContact}
          className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2"
        >
          <Phone className="w-4 h-4" />
          Emergency Contact
        </Button>
        
        <Button
          onClick={onContinueTextOnly}
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-50 flex items-center gap-2"
        >
          <MessageCircle className="w-4 h-4" />
          Continue with Text
        </Button>
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-200 rounded text-xs text-red-600">
          <strong>Technical Details:</strong> {error.message}
          {error.code && <span> (Code: {error.code})</span>}
        </div>
      )}
    </div>
  );

  const renderTextOnlyMode = () => (
    <div className="bg-yellow-50 border-l-4 border-yellow-500 rounded-lg p-6 mb-6">
      <div className="flex items-center space-x-3 mb-4">
        <MicOff className="w-6 h-6 text-yellow-600" />
        <div>
          <h2 className="text-lg font-semibold text-yellow-900">
            Audio Temporarily Unavailable
          </h2>
          <p className="text-sm text-yellow-700">
            Your consultation is continuing in text mode
          </p>
        </div>
      </div>

      <div className="bg-yellow-100 border border-yellow-200 rounded-lg p-4 mb-4">
        <h3 className="font-semibold text-yellow-800 mb-2">Text Mode Features:</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Type your symptoms and questions</li>
          <li>• Receive written responses from AI agents</li>
          <li>• Access all consultation features except voice</li>
          <li>• Automatic audio recovery attempts in background</li>
        </ul>
      </div>

      <div className="flex flex-wrap gap-3 mb-4">
        <Button
          onClick={onRetryAudio}
          disabled={isRecovering}
          className="flex items-center gap-2"
        >
          <Mic className="w-4 h-4" />
          {isRecovering ? 'Retrying Audio...' : 'Retry Audio'}
        </Button>
        
        <Button
          onClick={() => setShowRecoverySteps(!showRecoverySteps)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Troubleshooting Steps
        </Button>
      </div>

      {showRecoverySteps && (
        <div className="bg-yellow-100 border border-yellow-200 rounded-lg p-4 mb-4">
          <h4 className="font-semibold text-yellow-800 mb-2">Audio Recovery Steps:</h4>
          <ol className="text-sm text-yellow-700 space-y-2 list-decimal list-inside">
            <li>Check that your microphone is connected and working</li>
            <li>Ensure browser has microphone permissions</li>
            <li>Close other applications using your microphone</li>
            <li>Refresh the page and try again</li>
            <li>Try using a different browser or device</li>
          </ol>
        </div>
      )}

      {/* User feedback section */}
      <div className="bg-yellow-100 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-semibold text-yellow-800 mb-2">Help Us Improve:</h4>
        {!hasSubmittedFeedback ? (
          <div className="space-y-2">
            <textarea
              value={userFeedback}
              onChange={(e) => setUserFeedback(e.target.value)}
              placeholder="Tell us about the audio issue you experienced..."
              className="w-full p-2 border border-yellow-300 rounded text-sm"
              rows={3}
            />
            <Button
              onClick={handleSubmitFeedback}
              size="sm"
              disabled={!userFeedback.trim()}
            >
              Submit Feedback
            </Button>
          </div>
        ) : (
          <div className="flex items-center space-x-2 text-green-700">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">Thank you for your feedback!</span>
          </div>
        )}
      </div>

      {/* Render children (consultation interface) in text mode */}
      <div className="mt-6 opacity-90">
        {children}
      </div>
    </div>
  );

  const renderRecoveryMode = () => (
    <div className="bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 mb-6">
      <div className="flex items-center space-x-3 mb-4">
        <RefreshCw className={`w-6 h-6 text-blue-600 ${isRecovering ? 'animate-spin' : ''}`} />
        <div>
          <h2 className="text-lg font-semibold text-blue-900">
            Audio Recovery in Progress
          </h2>
          <p className="text-sm text-blue-700">
            Attempting to restore audio functionality
          </p>
        </div>
      </div>

      <div className="bg-blue-100 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-blue-800">Recovery Progress</span>
          <span className="text-sm text-blue-600">{recoveryProgress}%</span>
        </div>
        <div className="w-full bg-blue-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${recoveryProgress}%` }}
          />
        </div>
      </div>

      {recoveryProgress === 100 && (
        <div className="bg-green-100 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2 text-green-700">
            <CheckCircle className="w-5 h-5" />
            <span className="font-semibold">Audio Recovery Complete!</span>
          </div>
          <p className="text-sm text-green-600 mt-1">
            Audio functionality has been restored. You can now use voice features.
          </p>
        </div>
      )}

      <div className="text-sm text-blue-700">
        <p>Please wait while we:</p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Reinitialize audio context</li>
          <li>Test microphone connectivity</li>
          <li>Verify audio processing capabilities</li>
          <li>Restore voice consultation features</li>
        </ul>
      </div>
    </div>
  );

  // Main render logic
  switch (mode) {
    case 'emergency_mode':
      return renderEmergencyMode();
    case 'text_only':
      return renderTextOnlyMode();
    case 'recovery_mode':
      return renderRecoveryMode();
    default:
      return <div>{children}</div>;
  }
};

export default AudioFallbackUI;
