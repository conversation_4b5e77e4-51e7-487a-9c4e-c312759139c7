/**
 * COMPREHENSIVE TYPESCRIPT AUDIO SERVICES TESTS
 * 
 * Tests for TypeScript-converted audio services with focus on:
 * - Type safety and interface compliance
 * - Error boundary integration
 * - Emergency protocol functionality
 * - HIPAA compliance and security
 * - Performance requirements
 */

// Vitest globals are available via vitest.config.js globals: true
import speechToTextService from '../services/speechToTextService';
import textToSpeechService from '../services/textToSpeechService';
import aiOrchestrator from '../services/aiOrchestrator';
import audioStorageService from '../utils/audioStorageService';
import type {
  AudioBlob,
  SpeechToTextOptions,
  TextToSpeechOptions,
  AIOrchestrationOptions,
  ValidationResult,
  ServiceResponse,
  ChatMessage
} from '../types/audio';

// Mock implementations
const mockFetch = vi.fn();
const mockSupabase = {
  auth: {
    getSession: vi.fn(),
    getUser: vi.fn(),
    signOut: vi.fn()
  },
  storage: {
    from: vi.fn().mockReturnValue({
      upload: vi.fn(),
      getPublicUrl: vi.fn()
    })
  },
  from: vi.fn().mockReturnValue({
    insert: vi.fn(),
    select: vi.fn().mockReturnValue({
      eq: vi.fn().mockReturnValue({
        single: vi.fn()
      })
    })
  })
};

// Mock audio context
const mockAudioContext = {
  decodeAudioData: vi.fn(),
  close: vi.fn(),
  createOscillator: vi.fn(),
  createGain: vi.fn()
};

// Mock audio buffer
const mockAudioBuffer = {
  sampleRate: 44100,
  numberOfChannels: 2,
  duration: 5.0,
  getChannelData: vi.fn().mockReturnValue(new Float32Array(1024))
};

// Mock crypto
const mockCrypto = {
  getRandomValues: vi.fn(),
  subtle: {
    digest: vi.fn(),
    importKey: vi.fn(),
    deriveKey: vi.fn(),
    deriveBits: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn()
  }
};

describe('TypeScript Audio Services Integration Tests', () => {
  beforeEach(() => {
    // Setup global mocks using proper techniques
    global.fetch = mockFetch;

    // Use vi.stubGlobal for crypto API to avoid read-only property issues
    vi.stubGlobal('crypto', mockCrypto);

    global.AudioContext = vi.fn().mockImplementation(() => mockAudioContext);
    (global as any).webkitAudioContext = vi.fn().mockImplementation(() => mockAudioContext);

    // Mock Supabase client import
    vi.doMock('../utils/supabaseClient', () => ({
      supabase: mockSupabase
    }));

    // Reset all mocks
    vi.clearAllMocks();

    // Mock URL.createObjectURL for browser APIs
    Object.defineProperty(global, 'URL', {
      value: {
        createObjectURL: vi.fn().mockReturnValue('blob:mock-url-12345'),
        revokeObjectURL: vi.fn(),
        prototype: {},
        canParse: vi.fn().mockReturnValue(true),
        parse: vi.fn().mockReturnValue(null)
      },
      writable: true
    });

    // Setup default mock responses with proper structure
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      json: vi.fn().mockResolvedValue({
        success: true,
        data: {
          text: 'Test transcription',
          confidence: 0.95,
          language: 'en',
          duration: 2.5,
          processingTime: 1200,
          response: 'Test AI response',
          provider: 'openai'
        }
      }),
      blob: vi.fn().mockResolvedValue(new Blob(['test audio'], { type: 'audio/wav' })),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(1024))
    });

    // Enhanced Supabase authentication mocking
    mockSupabase.auth.getSession.mockResolvedValue({
      data: {
        session: {
          access_token: 'mock-access-token-12345',
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: { role: 'patient' }
          }
        }
      },
      error: null
    });

    mockSupabase.auth.getUser = vi.fn().mockResolvedValue({
      data: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          user_metadata: { role: 'patient' }
        }
      },
      error: null
    });

    mockSupabase.auth.signOut.mockResolvedValue({ error: null });

    // Mock crypto operations
    mockCrypto.subtle.digest.mockResolvedValue(new ArrayBuffer(32));
    mockCrypto.getRandomValues.mockImplementation((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    });

    // Mock audio context operations
    mockAudioContext.decodeAudioData.mockResolvedValue(mockAudioBuffer);
    mockAudioBuffer.getChannelData.mockReturnValue(new Float32Array(1024).fill(0.5));

    // Mock emergency authentication service
    vi.doMock('../services/emergencyAuthService', () => ({
      default: {
        validateEmergencyAuth: vi.fn().mockResolvedValue(true),
        createEmergencySession: vi.fn().mockResolvedValue({
          sessionId: 'emergency-session-id',
          emergencyToken: 'emergency-token-123',
          isActive: true,
          reason: 'test_emergency',
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
        }),
        getEmergencySession: vi.fn().mockReturnValue({
          sessionId: 'emergency-session-id',
          emergencyToken: 'emergency-token-123',
          isActive: true,
          reason: 'test_emergency'
        }),
        getEmergencyAuthStats: vi.fn().mockReturnValue({
          activeSessions: 1,
          availableTokens: 10,
          totalSessions: 1
        })
      }
    }));

    // Mock audit logger
    vi.doMock('../utils/auditLogger', () => ({
      default: {
        logEmergencyAccess: vi.fn().mockResolvedValue(undefined),
        logSystemError: vi.fn().mockResolvedValue(undefined),
        logSecurityEvent: vi.fn().mockResolvedValue(undefined)
      }
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('SpeechToTextService TypeScript Implementation', () => {
    it('should maintain type safety for transcription options', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const options: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        language: 'en',
        temperature: 0.2,
        sessionToken: 'mock-session-token'
      };

      const result = await speechToTextService.transcribeAudio(audioBlob, options);

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('timestamp');
      if (result.success && result.data) {
        expect(result.data).toHaveProperty('text');
        expect(result.data).toHaveProperty('confidence');
        expect(typeof result.data.confidence).toBe('number');
      }
    });

    it('should validate session ID format with TypeScript constraints', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const invalidOptions: SpeechToTextOptions = {
        sessionId: 'invalid-session-id',
        sessionToken: 'mock-token'
      };

      const result = await speechToTextService.transcribeAudio(audioBlob, invalidOptions);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid session ID format');
    });

    it('should handle emergency override with proper typing', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const emergencyOptions: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token',
        emergencyOverride: true
      };

      await speechToTextService.transcribeAudio(audioBlob, emergencyOptions);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Emergency-Override': 'true'
          })
        })
      );
    });

    it('should return properly typed validation results', () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      
      const validationResult = speechToTextService.validateAudioFile(audioBlob);

      expect(validationResult).toHaveProperty('valid');
      expect(validationResult).toHaveProperty('errors');
      expect(validationResult).toHaveProperty('warnings');
      expect(validationResult).toHaveProperty('metadata');
      expect(validationResult).toHaveProperty('securityChecks');
      expect(Array.isArray(validationResult.errors)).toBe(true);
      expect(Array.isArray(validationResult.warnings)).toBe(true);
    });

    it('should provide typed supported languages', () => {
      const languages = speechToTextService.getSupportedLanguages();

      expect(Array.isArray(languages)).toBe(true);
      languages.forEach(lang => {
        expect(lang).toHaveProperty('code');
        expect(lang).toHaveProperty('name');
        expect(lang).toHaveProperty('confidence');
        expect(typeof lang.confidence).toBe('number');
      });
    });
  });

  describe('TextToSpeechService TypeScript Implementation', () => {
    it('should maintain type safety for synthesis options', async () => {
      const text = 'Hello, this is a test message';
      const options: TextToSpeechOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        voiceId: 'ErXwobaYiN019PkySvjV',
        agentType: 'general-practitioner',
        stability: 0.5,
        similarity_boost: 0.75,
        sessionToken: 'mock-session-token'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: {
            audioData: btoa('mock audio data'),
            audioFormat: 'audio/mpeg',
            duration: 5.0,
            voiceId: 'ErXwobaYiN019PkySvjV',
            processingTime: 1500
          }
        })
      });

      const result = await textToSpeechService.synthesizeSpeech(text, options);

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data).toHaveProperty('audioBlob');
        expect(result.data).toHaveProperty('audioUrl');
        expect(result.data).toHaveProperty('duration');
        expect(result.data).toHaveProperty('voiceId');
        expect(typeof result.data.duration).toBe('number');
      }
    });

    it('should validate text length with TypeScript constraints', async () => {
      const longText = 'a'.repeat(6000); // Exceeds 5000 character limit
      const options: TextToSpeechOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      };

      const result = await textToSpeechService.synthesizeSpeech(longText, options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Text too long');
    });

    it('should return properly typed voice profiles', () => {
      const voices = textToSpeechService.getAvailableVoices();

      expect(Array.isArray(voices)).toBe(true);
      voices.forEach(voice => {
        expect(voice).toHaveProperty('id');
        expect(voice).toHaveProperty('name');
        expect(voice).toHaveProperty('agentType');
        expect(voice).toHaveProperty('language');
        expect(voice).toHaveProperty('gender');
        expect(voice).toHaveProperty('description');
        expect(typeof voice.stability).toBe('number');
        expect(typeof voice.similarity_boost).toBe('number');
      });
    });

    it('should estimate duration with proper typing', () => {
      const text = 'This is a test message for duration estimation';
      const estimatedDuration = textToSpeechService.estimateDuration(text);

      expect(typeof estimatedDuration).toBe('number');
      expect(estimatedDuration).toBeGreaterThan(0);
    });
  });

  describe('AIOrchestrator TypeScript Implementation', () => {
    it('should maintain type safety for orchestration options', async () => {
      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello, I need medical advice' },
        { role: 'assistant', content: 'I can help you with that' }
      ];

      const options: AIOrchestrationOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        messages,
        agentType: 'general-practitioner',
        maxTokens: 1000,
        temperature: 0.7
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: {
            content: 'AI response content',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 2000
          }
        })
      });

      const result = await aiOrchestrator.generateResponse(options);

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data).toHaveProperty('content');
        expect(result.data).toHaveProperty('agentType');
        expect(result.data).toHaveProperty('processingTime');
        expect(typeof result.data.processingTime).toBe('number');
      }
    });

    it('should validate message format with TypeScript constraints', async () => {
      const invalidMessages = [
        { role: 'invalid', content: 'test' } // Invalid role
      ] as any[];

      const options: AIOrchestrationOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        messages: invalidMessages
      };

      const result = await aiOrchestrator.generateResponse(options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid message role');
    });

    it('should handle conversation memory with proper typing', () => {
      const sessionId = '12345678-1234-1234-1234-123456789012';
      const memory = aiOrchestrator.getConversationMemory(sessionId);

      // Memory might be undefined for new sessions
      if (memory) {
        expect(memory).toHaveProperty('session_id');
        expect(memory).toHaveProperty('messages');
        expect(memory).toHaveProperty('last_activity');
        expect(memory).toHaveProperty('total_messages');
        expect(memory).toHaveProperty('participant_count');
        expect(memory).toHaveProperty('active_agents');
        expect(Array.isArray(memory.messages)).toBe(true);
        expect(Array.isArray(memory.active_agents)).toBe(true);
        expect(typeof memory.total_messages).toBe('number');
        expect(typeof memory.participant_count).toBe('number');
      }
    });

    it('should provide typed provider status', () => {
      const providerStatus = aiOrchestrator.getProviderStatus();

      expect(typeof providerStatus).toBe('object');
      Object.values(providerStatus).forEach(provider => {
        expect(provider).toHaveProperty('name');
        expect(provider).toHaveProperty('priority');
        expect(provider).toHaveProperty('available');
        expect(provider).toHaveProperty('failures');
        expect(typeof provider.priority).toBe('number');
        expect(typeof provider.available).toBe('boolean');
      });
    });
  });

  describe('AudioStorageService TypeScript Implementation', () => {
    it('should maintain type safety for storage operations', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const messageData = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        userId: 'user-123',
        speakerId: 'speaker-123',
        speakerName: 'Test Speaker',
        sessionToken: 'mock-session-token'
      };

      // Mock IndexedDB
      const mockDB = {
        transaction: vi.fn().mockReturnValue({
          objectStore: vi.fn().mockReturnValue({
            add: vi.fn().mockReturnValue({
              onsuccess: null,
              onerror: null
            })
          })
        })
      };

      // Mock the initializeDB method
      vi.spyOn(audioStorageService as any, 'initializeDB').mockResolvedValue(undefined);
      (audioStorageService as any).db = mockDB;

      // Mock encryption service
      const mockEncryptionService = {
        encryptMedicalData: vi.fn().mockResolvedValue({
          encrypted: true,
          algorithm: 'AES-256-GCM',
          keyLength: 256,
          iv: 'mock-iv',
          authTag: 'mock-auth-tag',
          encryptedData: 'mock-encrypted-data',
          timestamp: new Date().toISOString()
        })
      };

      // This would normally test the actual storage, but we'll test type safety
      expect(() => {
        audioStorageService.storeAudioMessage(audioBlob, messageData);
      }).not.toThrow();
    });

    it('should provide comprehensive validation with proper typing', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      Object.defineProperty(audioBlob, 'size', { value: 1024 * 100 }); // 100KB

      const validationResult = await audioStorageService.validateAudioFile(audioBlob);

      expect(validationResult).toHaveProperty('valid');
      expect(validationResult).toHaveProperty('errors');
      expect(validationResult).toHaveProperty('warnings');
      expect(validationResult).toHaveProperty('metadata');
      expect(validationResult).toHaveProperty('securityChecks');

      // Check metadata typing
      expect(validationResult.metadata).toHaveProperty('sampleRate');
      expect(validationResult.metadata).toHaveProperty('duration');
      expect(validationResult.metadata).toHaveProperty('channels');
      expect(typeof validationResult.metadata.sampleRate).toBe('number');

      // Check security checks typing
      expect(validationResult.securityChecks).toHaveProperty('safe');
      expect(validationResult.securityChecks).toHaveProperty('threats');
      expect(validationResult.securityChecks).toHaveProperty('checks');
      expect(typeof validationResult.securityChecks.safe).toBe('boolean');
    });

    it('should generate checksums with proper typing', async () => {
      const testData = new ArrayBuffer(1024);
      
      const checksum = await audioStorageService.generateAudioChecksum(testData);

      expect(typeof checksum).toBe('string');
      expect(checksum).toHaveLength(64); // SHA-256 hex string length
    });

    it('should provide typed sync status', async () => {
      const syncStatus = await audioStorageService.getSyncStatus();

      expect(syncStatus).toHaveProperty('pending');
      expect(syncStatus).toHaveProperty('synced');
      expect(syncStatus).toHaveProperty('total');
      expect(syncStatus).toHaveProperty('syncPercentage');
      expect(typeof syncStatus.pending).toBe('number');
      expect(typeof syncStatus.synced).toBe('number');
      expect(typeof syncStatus.total).toBe('number');
      expect(typeof syncStatus.syncPercentage).toBe('number');
    });
  });

  describe('Emergency Protocol Integration', () => {
    it('should trigger emergency protocols with proper typing', async () => {
      const mockAuditLogger = {
        logEmergencyAccess: vi.fn().mockResolvedValue(undefined)
      };

      // Mock dynamic import
      vi.doMock('../utils/auditLogger', () => ({
        default: mockAuditLogger
      }));

      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const emergencyOptions: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token',
        emergencyOverride: true
      };

      // Simulate slow response to trigger emergency protocol
      mockFetch.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: false,
          status: 500
        }), 3000))
      );

      const result = await speechToTextService.transcribeAudio(audioBlob, emergencyOptions);

      expect(result.success).toBe(false);
      // Emergency protocol should have been triggered due to timeout
    });
  });

  describe('Performance Requirements', () => {
    it('should complete TypeScript service operations within acceptable time limits', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const options: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      };

      const startTime = performance.now();
      
      await speechToTextService.transcribeAudio(audioBlob, options);
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      // TypeScript overhead should not significantly impact performance
      expect(duration).toBeLessThan(100); // Should complete quickly with mocks
    });

    it('should maintain emergency response time requirements', async () => {
      const emergencyStartTime = performance.now();
      
      // Test emergency stop mechanism
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const emergencyOptions: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token',
        emergencyOverride: true
      };

      await speechToTextService.transcribeAudio(audioBlob, emergencyOptions);
      
      const emergencyEndTime = performance.now();
      const emergencyDuration = emergencyEndTime - emergencyStartTime;

      // Emergency operations should complete within 2 seconds
      expect(emergencyDuration).toBeLessThan(2000);
    });
  });

  describe('Type Safety Validation', () => {
    it('should enforce readonly properties where appropriate', () => {
      const voices = textToSpeechService.getAvailableVoices();
      
      // Attempt to modify readonly array should be prevented by TypeScript
      // This test validates that our types are properly defined
      expect(Array.isArray(voices)).toBe(true);
      expect(voices.length).toBeGreaterThan(0);
    });

    it('should provide proper error typing', async () => {
      const audioBlob = new Blob(['mock audio'], { type: 'audio/webm' }) as AudioBlob;
      const invalidOptions = {
        sessionId: '', // Invalid session ID
        sessionToken: 'mock-token'
      } as SpeechToTextOptions;

      const result = await speechToTextService.transcribeAudio(audioBlob, invalidOptions);

      expect(result.success).toBe(false);
      expect(typeof result.error).toBe('string');
      expect(typeof result.code).toBe('string');
      expect(typeof result.timestamp).toBe('string');
    });
  });
});
