/**
 * HIPAA-COMPLIANT MEDICAL DATA TYPE DEFINITIONS
 * 
 * This file contains strict TypeScript type definitions for all medical data
 * structures to ensure type safety and prevent data corruption that could
 * compromise patient care.
 * 
 * SAFETY REQUIREMENTS:
 * - All medical data must be strongly typed
 * - No 'any' types allowed for medical data
 * - Strict validation for critical fields
 * - Immutable data structures where possible
 * - Clear distinction between encrypted and decrypted data
 */

// Base types for medical data validation
export type MedicalDataStatus = 'active' | 'inactive' | 'archived';
export type MedicalSeverity = 'mild' | 'moderate' | 'severe' | 'critical';
export type MedicalUrgency = 'low' | 'medium' | 'high' | 'emergency';

// User roles for RBAC
export type UserRole = 'patient' | 'healthcare_provider' | 'admin' | 'emergency_responder';

// Medical condition types
export interface MedicalCondition {
  readonly id: string;
  readonly user_id: string;
  readonly condition_name: string;
  readonly diagnosed_date: string; // ISO date string
  readonly is_current: boolean;
  readonly severity: MedicalSeverity;
  readonly notes?: string | undefined;
  readonly created_at: string; // ISO date string
  readonly updated_at?: string | undefined; // ISO date string
  readonly status: MedicalDataStatus;
}

// Medication types
export interface Medication {
  readonly id: string;
  readonly user_id: string;
  readonly medication_name: string;
  readonly dosage: string;
  readonly frequency: string;
  readonly start_date: string; // ISO date string
  readonly end_date?: string; // ISO date string
  readonly is_current: boolean;
  readonly prescribing_doctor?: string;
  readonly notes?: string;
  readonly created_at: string; // ISO date string
  readonly updated_at?: string; // ISO date string
  readonly status: MedicalDataStatus;
}

// Symptom tracking
export interface Symptom {
  readonly id: string;
  readonly user_id: string;
  readonly symptom_name: string;
  readonly severity: MedicalSeverity;
  readonly onset_date: string; // ISO date string
  readonly duration?: string;
  readonly triggers?: string[];
  readonly notes?: string;
  readonly created_at: string; // ISO date string
  readonly status: MedicalDataStatus;
}

// Vital signs tracking
export interface VitalSigns {
  readonly bloodPressure?: { systolic: number; diastolic: number };
  readonly heartRate?: number;
  readonly temperature?: number;
  readonly respiratoryRate?: number;
  readonly oxygenSaturation?: number;
  readonly weight?: number;
  readonly height?: number;
  readonly bmi?: number;
  readonly painScale?: number;
}

// Patient profile
export interface PatientProfile {
  readonly id: string;
  readonly user_id: string;
  readonly full_name: string;
  readonly date_of_birth: string; // ISO date string
  readonly gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  readonly blood_type?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
  readonly allergies?: string[];
  readonly emergency_contact_name?: string;
  readonly emergency_contact_phone?: string;
  readonly medical_history?: string;
  readonly created_at: string; // ISO date string
  readonly updated_at?: string; // ISO date string
}

// Consultation types
export type ConsultationStatus = 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
export type AgentSpecialty = 'general_practitioner' | 'cardiologist' | 'nutritionist' | 'psychiatrist' | 'dermatologist' | 'neurologist';

export interface Consultation {
  readonly id: string;
  readonly patient_id: string;
  readonly provider_id?: string;
  readonly agent_specialty: AgentSpecialty;
  readonly status: ConsultationStatus;
  readonly scheduled_at?: string; // ISO date string
  readonly started_at?: string; // ISO date string
  readonly completed_at?: string; // ISO date string
  readonly duration_minutes?: number;
  readonly chief_complaint?: string;
  readonly diagnosis?: string;
  readonly treatment_plan?: string;
  readonly follow_up_required: boolean;
  readonly follow_up_date?: string; // ISO date string
  readonly notes?: string;
  readonly created_at: string; // ISO date string
  readonly updated_at?: string; // ISO date string
}

// Encrypted data wrapper
export interface EncryptedMedicalData<T = unknown> {
  readonly encrypted: true;
  readonly data: string; // Base64 encoded encrypted data
  readonly iv: string; // Base64 encoded initialization vector
  readonly salt: string; // Base64 encoded salt
  readonly timestamp: number;
  readonly algorithm: 'AES-GCM';
  readonly keyLength: 256;
  readonly dataType: string; // Original data type for validation
}

// Unencrypted data wrapper (for development/testing only)
export interface UnencryptedMedicalData<T> {
  readonly encrypted: false;
  readonly data: T;
  readonly timestamp: number;
}

// Union type for medical data storage
export type StoredMedicalData<T> = EncryptedMedicalData<T> | UnencryptedMedicalData<T>;

// API Response types
export interface MedicalDataResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly source?: 'online' | 'offline';
  readonly message?: string;
  readonly offline?: boolean;
}

// Validation result types
export interface ValidationResult {
  readonly valid: boolean;
  readonly sanitized?: string | undefined;
  readonly error?: string | undefined;
  readonly warning?: string | undefined;
  readonly isKnownMedication?: boolean | undefined;
}

// Audit log types (for HIPAA compliance)
export type AuditEventType = 
  | 'medical_data_access'
  | 'medical_data_create'
  | 'medical_data_update'
  | 'medical_data_delete'
  | 'user_login'
  | 'user_logout'
  | 'authentication_failure'
  | 'system_access'
  | 'data_export'
  | 'emergency_access';

export interface AuditLogEntry {
  readonly id: string;
  readonly event_type: AuditEventType;
  readonly user_id?: string;
  readonly resource_type: string;
  readonly resource_id: string;
  readonly action: string;
  readonly success: boolean;
  readonly ip_address?: string;
  readonly user_agent?: string;
  readonly metadata?: Record<string, unknown>;
  readonly timestamp: string; // ISO date string
  readonly session_id?: string;
}

// Error types for medical operations
export class MedicalDataError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    public readonly patientSafetyImpact: boolean = false
  ) {
    super(message);
    this.name = 'MedicalDataError';
  }
}

export class EncryptionError extends Error {
  constructor(
    message: string,
    public readonly operation: 'encrypt' | 'decrypt',
    public readonly dataType: string
  ) {
    super(message);
    this.name = 'EncryptionError';
  }
}

// Missing type definitions that are referenced throughout the codebase
export type MedicalDataPriority = 'low' | 'normal' | 'high' | 'critical' | 'emergency';

export interface BaseMedicalEntity {
  readonly id: string;
  readonly user_id: string;
  readonly created_at: string;
  readonly updated_at?: string | undefined;
  readonly status: MedicalDataStatus;
}

export interface MedicalDataSearchCriteria {
  query?: string | undefined;
  date_range?: {
    start_date: string;
    end_date: string;
  } | undefined;
  priority?: MedicalDataPriority[] | undefined;
  status?: MedicalDataStatus[] | undefined;
  emergency_only?: boolean | undefined;
  verified_only?: boolean | undefined;
  limit?: number | undefined;
  offset?: number | undefined;
}

export interface MedicalDataListResponse<T> {
  readonly success: boolean;
  readonly data?: T[] | undefined;
  readonly error?: string | undefined;
  readonly source?: 'online' | 'offline' | undefined;
  readonly message?: string | undefined;
  readonly offline?: boolean | undefined;
  readonly total_count?: number | undefined;
  readonly page?: number | undefined;
  readonly page_size?: number | undefined;
  readonly has_more?: boolean | undefined;
  readonly performance_ms?: number | undefined;
}

// Service interfaces
export interface MedicalDataService {
  getUserConditions(userId: string): Promise<MedicalDataResponse<MedicalCondition[]>>;
  addCondition(userId: string, condition: Omit<MedicalCondition, 'id' | 'user_id' | 'created_at'>): Promise<MedicalDataResponse<MedicalCondition>>;
  updateCondition(conditionId: string, updates: Partial<Pick<MedicalCondition, 'condition_name' | 'severity' | 'notes' | 'is_current'>>): Promise<MedicalDataResponse<MedicalCondition>>;
  deleteCondition(conditionId: string): Promise<MedicalDataResponse<void>>;

  getUserMedications(userId: string): Promise<MedicalDataResponse<Medication[]>>;
  addMedication(userId: string, medication: Omit<Medication, 'id' | 'user_id' | 'created_at'>): Promise<MedicalDataResponse<Medication>>;
  updateMedication(medicationId: string, updates: Partial<Pick<Medication, 'dosage' | 'frequency' | 'notes' | 'is_current'>>): Promise<MedicalDataResponse<Medication>>;
  deleteMedication(medicationId: string): Promise<MedicalDataResponse<void>>;
}

export interface EncryptionService {
  encryptMedicalData<T>(data: T, sessionToken: string): Promise<EncryptedMedicalData<T> | UnencryptedMedicalData<T>>;
  decryptMedicalData<T>(encryptedData: EncryptedMedicalData<T> | UnencryptedMedicalData<T>, sessionToken: string): Promise<T>;
  isEncryptionSupported(): boolean;
}

export interface AuditLogger {
  logMedicalDataAccess(action: string, resourceType: string, resourceId: string, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logConditionAccess(action: string, conditionId: string, success: boolean, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logMedicationAccess(action: string, medicationId: string, success: boolean, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logConsultationAccess(action: string, consultationId: string, success: boolean, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logLogin(success: boolean, method?: string | undefined, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logLoginFailure(reason: string, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logSecurityEvent(eventType: string, severity: 'low' | 'medium' | 'high' | 'critical', metadata?: Record<string, unknown> | undefined): Promise<void>;
  logDatabaseError(operation: string, error: Error | string, metadata?: Record<string, unknown> | undefined): Promise<void>;
  logPerformanceMetric(metric: {
    feature: string;
    operation: string;
    duration: number;
    success: boolean;
    sessionId?: string | undefined;
    userId?: string | undefined;
    timestamp: string;
  }): Promise<void>;
}
