/**
 * MEDICAL ERROR PROVIDER
 * 
 * Comprehensive error boundary provider that wraps the entire medical
 * application with specialized error handling for different types of
 * failures, prioritizing patient safety above all else.
 * 
 * LAYERED ERROR PROTECTION:
 * 1. Network Error Boundary - Handles connectivity issues
 * 2. Encryption Error Boundary - Handles data security failures
 * 3. Medical Error Boundary - Handles general medical component errors
 * 4. Global Error Handler - Catches any remaining errors
 * 
 * PATIENT SAFETY FEATURES:
 * - Never lose medical data during errors
 * - Provide emergency access to critical functions
 * - Maintain audit trail for all errors
 * - Offer multiple recovery mechanisms
 * - Emergency contact integration
 */

import React, { ReactNode, useEffect, useState } from 'react';
import MedicalErrorBoundary from './MedicalErrorBoundary';
import EncryptionErrorBoundary from './EncryptionErrorBoundary';
import NetworkErrorBoundary from './NetworkErrorBoundary';
import { MedicalDataError, EncryptionError, NetworkError } from '../../types';
import auditLogger from '../../utils/auditLogger';

interface MedicalErrorProviderProps {
  readonly children: ReactNode;
  readonly emergencyContact?: string;
  readonly enableOfflineMode?: boolean;
  readonly enableEncryptionFallback?: boolean;
  readonly onCriticalError?: (error: Error, context: string) => void;
}

interface ErrorStats {
  readonly totalErrors: number;
  readonly networkErrors: number;
  readonly encryptionErrors: number;
  readonly medicalErrors: number;
  readonly lastError: string | null;
}

export const MedicalErrorProvider: React.FC<MedicalErrorProviderProps> = ({
  children,
  emergencyContact,
  enableOfflineMode = true,
  enableEncryptionFallback = false,
  onCriticalError
}) => {
  const [errorStats, setErrorStats] = useState<ErrorStats>({
    totalErrors: 0,
    networkErrors: 0,
    encryptionErrors: 0,
    medicalErrors: 0,
    lastError: null
  });

  const [showErrorStats, setShowErrorStats] = useState(false);

  // Global error handler for uncaught errors
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      console.error('Global error caught:', event?.error);
      
      // Log global error
      auditLogger.logMedicalDataAccess(
        'global_error',
        'application',
        crypto.randomUUID(),
        {
          error_message: event.error?.message || 'Unknown global error',
          error_stack: event.error?.stack,
          filename: event?.filename,
          line_number: event?.lineno,
          column_number: event?.colno,
          timestamp: new Date().toISOString()
        }
      );

      // Update error stats
      setErrorStats(prev => ({
        ...prev,
        totalErrors: prev.totalErrors + 1,
        lastError: event.error?.message || 'Global error'
      }));

      // Notify critical error handler
      onCriticalError?.(event?.error, 'global');
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event?.reason);
      
      // Log unhandled rejection
      auditLogger.logMedicalDataAccess(
        'unhandled_rejection',
        'application',
        crypto.randomUUID(),
        {
          rejection_reason: event.reason?.toString() || 'Unknown rejection',
          timestamp: new Date().toISOString()
        }
      );

      // Update error stats
      setErrorStats(prev => ({
        ...prev,
        totalErrors: prev.totalErrors + 1,
        lastError: event.reason?.toString() || 'Promise rejection'
      }));

      // Notify critical error handler
      onCriticalError?.(new Error(event?.reason), 'promise_rejection');
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onCriticalError]);

  // Handle network errors
  const handleNetworkError = (error: NetworkError, offlineMode: boolean) => {
    console.log('Network error handled:', error?.message, 'Offline mode:', offlineMode);
    
    setErrorStats(prev => ({
      ...prev,
      totalErrors: prev.totalErrors + 1,
      networkErrors: prev.networkErrors + 1,
      lastError: error.message
    }));

    // Check if this is a critical error
    if (error.message.includes('critical') || error.message.includes('emergency')) {
      const errorWithName = Object.assign(new Error(error?.message), error) as Error;
      onCriticalError?.(errorWithName, 'network');
    }
  };

  // Handle encryption errors
  const handleEncryptionError = (error: EncryptionError, recoveredData?: unknown) => {
    console.log('Encryption error handled:', error?.message, 'Data recovered:', !!recoveredData);
    
    setErrorStats(prev => ({
      ...prev,
      totalErrors: prev.totalErrors + 1,
      encryptionErrors: prev.encryptionErrors + 1,
      lastError: error.message
    }));

    // Encryption errors are always considered critical
    onCriticalError?.(error, 'encryption');
  };

  // Handle medical component errors
  const handleMedicalError = (error: Error, errorInfo: React.ErrorInfo, preservedData?: unknown) => {
    console.log('Medical error handled:', error?.message, 'Data preserved:', !!preservedData);
    
    setErrorStats(prev => ({
      ...prev,
      totalErrors: prev.totalErrors + 1,
      medicalErrors: prev.medicalErrors + 1,
      lastError: error.message
    }));

    // Check if this is a critical medical error
    if (error instanceof MedicalDataError && error?.patientSafetyImpact) {
      onCriticalError?.(error, 'medical');
    }
  };

  // Handle recovery actions
  const handleRecovery = (recoveryMethod: string) => {
    console.log('Recovery action taken:', recoveryMethod);
    
    // Log recovery action
    auditLogger.logMedicalDataAccess(
      'error_recovery',
      'error_boundary',
      crypto.randomUUID(),
      {
        recovery_method: recoveryMethod,
        error_stats: errorStats,
        timestamp: new Date().toISOString()
      }
    );
  };

  // Toggle error stats display (for development/admin)
  const toggleErrorStats = () => {
    setShowErrorStats(!showErrorStats);
  };

  return (
    <>
      {/* Layered Error Boundaries */}
      <NetworkErrorBoundary
        onNetworkError={handleNetworkError}
        enableOfflineMode={enableOfflineMode}
        criticalOperation={false}
      >
        <EncryptionErrorBoundary
          onEncryptionError={handleEncryptionError}
          allowUnencryptedFallback={enableEncryptionFallback}
          emergencyExportEnabled={true}
        >
          <MedicalErrorBoundary
            componentName="MedicalErrorProvider"
            medicalContext={{
              emergencyContact: emergencyContact || undefined
            }}
            onError={handleMedicalError}
            onRecovery={handleRecovery}
            enableEmergencyMode={true}
            preserveDataOnError={true}
          >
            {children}
          </MedicalErrorBoundary>
        </EncryptionErrorBoundary>
      </NetworkErrorBoundary>

      {/* Error Stats Display (for development/admin) */}
      {showErrorStats && (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900">Error Statistics</h4>
            <button
              onClick={toggleErrorStats}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex justify-between">
              <span>Total Errors:</span>
              <span className="font-medium">{errorStats.totalErrors}</span>
            </div>
            <div className="flex justify-between">
              <span>Network:</span>
              <span className="font-medium">{errorStats.networkErrors}</span>
            </div>
            <div className="flex justify-between">
              <span>Encryption:</span>
              <span className="font-medium">{errorStats.encryptionErrors}</span>
            </div>
            <div className="flex justify-between">
              <span>Medical:</span>
              <span className="font-medium">{errorStats.medicalErrors}</span>
            </div>
            {errorStats.lastError && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="text-xs text-gray-500">Last Error:</div>
                <div className="text-xs text-red-600 truncate" title={errorStats.lastError}>
                  {errorStats.lastError}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Stats Toggle Button (hidden in production) */}
      {process.env.NODE_ENV === 'development' && (
        <button
          onClick={toggleErrorStats}
          className="fixed bottom-4 left-4 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700"
          title="Toggle Error Statistics"
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      )}
    </>
  );
};

export default MedicalErrorProvider;
