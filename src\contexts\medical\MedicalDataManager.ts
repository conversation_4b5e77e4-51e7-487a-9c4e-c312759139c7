/**
 * MEDICAL DATA MANAGER
 * 
 * Handles medical data operations, caching, and synchronization.
 * Extracted from OptimizedMedicalDataContext for better maintainability.
 */

import type { MedicalCondition, Medication, Symptom } from '../../types/medical';
import type { MedicalDataState, CacheConfig, SyncOptions } from './MedicalDataTypes';
import { initialMedicalDataState } from './MedicalDataTypes';
import auditLogger from '../../utils/auditLogger';

export class MedicalDataManager {
  private state: MedicalDataState = { ...initialMedicalDataState };
  private cache = new Map<string, any>();
  private syncTimer?: NodeJS.Timeout;
  
  constructor(private config: CacheConfig) {
    this.setupSyncTimer();
  }

  /**
   * Get current state
   */
  getState(): MedicalDataState {
    return { ...this.state };
  }

  /**
   * Update state and notify listeners
   */
  private setState(updates: Partial<MedicalDataState>, callback?: () => void): void {
    this.state = { ...this.state, ...updates };
    if (callback) {
      callback();
    }
  }

  /**
   * Add medical condition
   */
  async addCondition(condition: MedicalCondition): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      // Add to local state
      const newConditions = [...this.state.conditions, condition];
      this.setState({ 
        conditions: newConditions,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      // Cache the condition
      this.cache.set(`condition_${condition.id}`, condition);

      // Log for audit
      await auditLogger.logDataAccess(
        'medical_condition',
        condition.id,
        true,
        { action: 'create' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to add condition',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Update medical condition
   */
  async updateCondition(id: string, updates: Partial<MedicalCondition>): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      const conditionIndex = this.state.conditions.findIndex(c => c.id === id);
      if (conditionIndex === -1) {
        throw new Error('Condition not found');
      }

      const updatedConditions = [...this.state.conditions];
      updatedConditions[conditionIndex] = { 
        ...updatedConditions[conditionIndex], 
        ...updates 
      };

      this.setState({ 
        conditions: updatedConditions,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      // Update cache
      this.cache.set(`condition_${id}`, updatedConditions[conditionIndex]);

      await auditLogger.logDataAccess(
        'medical_condition',
        id,
        true,
        { action: 'update' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to update condition',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Remove medical condition
   */
  async removeCondition(id: string): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      const newConditions = this.state.conditions.filter(c => c.id !== id);
      this.setState({ 
        conditions: newConditions,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      // Remove from cache
      this.cache.delete(`condition_${id}`);

      await auditLogger.logDataAccess(
        'medical_condition',
        id,
        true,
        { action: 'delete' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to remove condition',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Add medication
   */
  async addMedication(medication: Medication): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      const newMedications = [...this.state.medications, medication];
      this.setState({ 
        medications: newMedications,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      this.cache.set(`medication_${medication.id}`, medication);

      await auditLogger.logDataAccess(
        'medication',
        medication.id,
        true,
        { action: 'create' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to add medication',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Update medication
   */
  async updateMedication(id: string, updates: Partial<Medication>): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      const medicationIndex = this.state.medications.findIndex(m => m.id === id);
      if (medicationIndex === -1) {
        throw new Error('Medication not found');
      }

      const updatedMedications = [...this.state.medications];
      updatedMedications[medicationIndex] = { 
        ...updatedMedications[medicationIndex], 
        ...updates 
      };

      this.setState({ 
        medications: updatedMedications,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      this.cache.set(`medication_${id}`, updatedMedications[medicationIndex]);

      await auditLogger.logDataAccess(
        'medication',
        id,
        true,
        { action: 'update' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to update medication',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Remove medication
   */
  async removeMedication(id: string): Promise<void> {
    try {
      this.setState({ isLoading: true });
      
      const newMedications = this.state.medications.filter(m => m.id !== id);
      this.setState({ 
        medications: newMedications,
        lastUpdated: Date.now(),
        isLoading: false 
      });

      this.cache.delete(`medication_${id}`);

      await auditLogger.logDataAccess(
        'medication',
        id,
        true,
        { action: 'delete' }
      );

    } catch (error) {
      this.setState({ 
        error: error instanceof Error ? error.message : 'Failed to remove medication',
        isLoading: false 
      });
      throw error;
    }
  }

  /**
   * Sync data with server
   */
  async syncData(options: SyncOptions = {}): Promise<void> {
    try {
      this.setState({ syncStatus: 'syncing' });
      
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.setState({ 
        syncStatus: 'idle',
        lastUpdated: Date.now() 
      });

    } catch (error) {
      this.setState({ syncStatus: 'error' });
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Setup automatic sync timer
   */
  private setupSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    this.syncTimer = setInterval(() => {
      this.syncData({ priority: 'low' }).catch(console.error);
    }, this.config.syncInterval);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    this.cache.clear();
  }
}
