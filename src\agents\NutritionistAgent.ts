/**
 * NUTRITIONIST AGENT
 * 
 * Specialized AI agent for nutrition counseling, dietary planning, and metabolic health.
 * Provides expert guidance on nutrition for disease prevention, management, and optimal health.
 * 
 * SPECIALIZATION AREAS:
 * - Personalized nutrition planning and dietary counseling
 * - Medical nutrition therapy for chronic diseases
 * - Weight management and metabolic health optimization
 * - Diabetes nutrition management and carbohydrate counting
 * - Cardiovascular nutrition and heart-healthy eating
 * - Sports nutrition and performance optimization
 * - Eating disorder support and recovery nutrition
 * - Food allergies and intolerances management
 */

import { BaseAgent } from './BaseAgent';
import type { 
  AgentRequest, 
  AgentResponse, 
  AgentRole, 
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class NutritionistAgent extends BaseAgent {
  constructor(memoryManager: MemoryManager) {
    const id = 'nutritionist-agent-001';
    const name = 'Dr. <PERSON>';
    const role: AgentRole = 'nutritionist';
    const capabilities: AgentCapability[] = [
      'specialist_consultation',
      'treatment_planning',
      'patient_education',
      'chronic_disease_management',
      'preventive_care'
    ];

    const systemPrompt = `You are Dr. <PERSON>, a Registered Dietitian Nutritionist (RDN) with a PhD in Nutritional Sciences and 15 years of experience in clinical nutrition and medical nutrition therapy.

CORE EXPERTISE:
- Medical nutrition therapy for chronic diseases (diabetes, cardiovascular disease, kidney disease)
- Weight management and metabolic health optimization
- Sports nutrition and performance enhancement
- Eating disorder treatment and recovery nutrition
- Food allergies, intolerances, and elimination diets
- Pediatric and geriatric nutrition
- Plant-based and therapeutic diets
- Nutritional biochemistry and metabolism

ASSESSMENT APPROACH:
- Comprehensive nutritional assessment including dietary intake, medical history, and lifestyle factors
- Evidence-based nutrition recommendations following current dietary guidelines
- Personalized meal planning considering individual preferences, cultural background, and medical needs
- Practical, sustainable dietary modifications that fit into real-life situations
- Integration of nutrition therapy with overall medical treatment plans

SPECIALIZATION AREAS:
- **Diabetes Management**: Carbohydrate counting, blood sugar control, insulin-to-carb ratios
- **Cardiovascular Nutrition**: Heart-healthy eating patterns, cholesterol management, DASH diet
- **Weight Management**: Sustainable weight loss/gain strategies, behavior modification
- **Gastrointestinal Health**: IBS, IBD, GERD nutrition management
- **Renal Nutrition**: CKD diet modifications, dialysis nutrition
- **Oncology Nutrition**: Cancer treatment support, managing side effects

COMMUNICATION STYLE:
- Empathetic and non-judgmental approach to food and eating
- Clear, practical nutrition education using everyday language
- Focus on sustainable lifestyle changes rather than restrictive dieting
- Collaborative goal-setting with patients
- Cultural sensitivity and respect for food traditions

SAFETY PRIORITIES:
- Recognition of eating disorder red flags and appropriate referrals
- Safe weight management practices
- Identification of nutritional deficiencies and malnutrition risk
- Appropriate supplementation recommendations
- Coordination with medical team for complex conditions

COLLABORATION FOCUS:
- Work closely with physicians for medical nutrition therapy
- Coordinate with endocrinologists for diabetes management
- Partner with cardiologists for heart-healthy nutrition
- Collaborate with mental health professionals for eating disorders
- Engage with fitness professionals for comprehensive wellness

Remember: Nutrition is highly individualized. While I can provide evidence-based guidance, optimal nutrition plans should be personalized based on individual health status, preferences, and goals. Always encourage patients to work with healthcare providers for comprehensive care.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager);
  }

  /**
   * Handle nutrition consultation requests
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🥗 Nutritionist processing request for session: ${request.sessionId}`);

      // Check for nutrition-related emergencies (eating disorders, severe malnutrition)
      const emergencyFlags = this.detectNutritionEmergencies(request.userMessage);
      
      // Assess nutritional needs and goals
      const nutritionAssessment = this.assessNutritionalNeeds(request);

      // Generate specialized nutrition response
      const response = await this.generateNutritionResponse(request, nutritionAssessment);

      // Determine if other specialists needed
      const handoffSuggestions = this.analyzeForSpecialistCollaboration(request);

      // Calculate confidence based on nutrition complexity
      const confidence = this.calculateNutritionConfidence(request);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, confidence, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence,
        reasoning: 'Nutritional assessment completed with evidence-based dietary guidance',
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: this.generateNutritionFollowUp(request, nutritionAssessment),
        metadata: {
          responseTime,
          assessmentType: 'nutrition_consultation',
          nutritionFocus: nutritionAssessment.primaryFocus,
          interventionLevel: nutritionAssessment.interventionLevel
        }
      };

    } catch (error) {
      console.error('❌ Nutritionist Agent error:', error);
      
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I apologize, but I'm experiencing technical difficulties. For urgent nutrition concerns or eating disorder symptoms, please consult with a registered dietitian or healthcare provider directly.",
        confidence: 0.1,
        reasoning: 'Technical error during nutrition consultation',
        emergencyFlags: [{
          type: 'medical_emergency',
          severity: 'medium',
          description: 'System error - recommend direct nutrition consultation',
          recommendedAction: 'Consult registered dietitian or healthcare provider',
          timeToResponse: 1500
        }]
      };
    }
  }

  /**
   * Detect nutrition-related emergencies
   */
  private detectNutritionEmergencies(message: string): EmergencyFlag[] {
    const emergencyFlags: EmergencyFlag[] = [];
    const lowerMessage = message.toLowerCase();

    // Eating disorder red flags
    const eatingDisorderFlags = [
      'not eating', 'starving myself', 'binge eating', 'purging', 'vomiting after eating',
      'laxative abuse', 'extreme weight loss', 'afraid to eat', 'obsessed with calories'
    ];

    // Severe malnutrition indicators
    const malnutritionFlags = [
      'severe weight loss', 'cannot keep food down', 'not eating for days',
      'extreme fatigue', 'hair falling out', 'dizzy when standing'
    ];

    // Check for eating disorder emergencies
    eatingDisorderFlags.forEach(flag => {
      if (lowerMessage.includes(flag)) {
        emergencyFlags.push({
          type: 'mental_health_crisis',
          severity: 'high',
          description: `Eating disorder warning sign detected: ${flag}`,
          recommendedAction: 'Seek immediate eating disorder treatment or mental health support',
          timeToResponse: 1000
        });
      }
    });

    // Check for malnutrition emergencies
    malnutritionFlags.forEach(flag => {
      if (lowerMessage.includes(flag)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'high',
          description: `Severe malnutrition indicator: ${flag}`,
          recommendedAction: 'Seek immediate medical attention for nutritional assessment',
          timeToResponse: 1000
        });
      }
    });

    return emergencyFlags;
  }

  /**
   * Assess nutritional needs and goals
   */
  private assessNutritionalNeeds(request: AgentRequest): NutritionAssessment {
    const message = request.userMessage.toLowerCase();
    
    // Determine primary nutrition focus
    let primaryFocus: string = 'general_wellness';
    let interventionLevel: 'basic' | 'moderate' | 'intensive' = 'basic';
    const nutritionGoals: string[] = [];
    const medicalConditions: string[] = [];

    // Medical nutrition therapy needs
    const medicalNutritionKeywords = [
      { keyword: 'diabetes', focus: 'diabetes_management', level: 'intensive' },
      { keyword: 'heart disease', focus: 'cardiovascular_nutrition', level: 'intensive' },
      { keyword: 'high cholesterol', focus: 'cardiovascular_nutrition', level: 'moderate' },
      { keyword: 'high blood pressure', focus: 'cardiovascular_nutrition', level: 'moderate' },
      { keyword: 'kidney disease', focus: 'renal_nutrition', level: 'intensive' },
      { keyword: 'ibs', focus: 'gastrointestinal_nutrition', level: 'moderate' },
      { keyword: 'crohn', focus: 'gastrointestinal_nutrition', level: 'intensive' },
      { keyword: 'celiac', focus: 'gluten_free_nutrition', level: 'moderate' }
    ];

    // Weight management goals
    const weightGoals = [
      { keyword: 'lose weight', goal: 'weight_loss', focus: 'weight_management' },
      { keyword: 'gain weight', goal: 'weight_gain', focus: 'weight_management' },
      { keyword: 'maintain weight', goal: 'weight_maintenance', focus: 'weight_management' }
    ];

    // Performance nutrition
    const performanceKeywords = [
      { keyword: 'athlete', focus: 'sports_nutrition', level: 'moderate' },
      { keyword: 'workout', focus: 'sports_nutrition', level: 'basic' },
      { keyword: 'muscle building', focus: 'sports_nutrition', level: 'moderate' }
    ];

    // Assess medical conditions
    medicalNutritionKeywords.forEach(item => {
      if (message.includes(item.keyword)) {
        primaryFocus = item.focus;
        interventionLevel = item.level as 'basic' | 'moderate' | 'intensive';
        medicalConditions.push(item.keyword);
      }
    });

    // Assess weight goals
    weightGoals.forEach(item => {
      if (message.includes(item.keyword)) {
        if (primaryFocus === 'general_wellness') {
          primaryFocus = item.focus;
        }
        nutritionGoals.push(item.goal);
      }
    });

    // Assess performance goals
    performanceKeywords.forEach(item => {
      if (message.includes(item.keyword)) {
        if (primaryFocus === 'general_wellness') {
          primaryFocus = item.focus;
          interventionLevel = item.level as 'basic' | 'moderate' | 'intensive';
        }
      }
    });

    return {
      primaryFocus,
      interventionLevel,
      nutritionGoals,
      medicalConditions,
      recommendedApproach: this.getRecommendedApproach(primaryFocus, interventionLevel)
    };
  }

  /**
   * Generate specialized nutrition response
   */
  private async generateNutritionResponse(request: AgentRequest, assessment: NutritionAssessment): Promise<string> {
    const hasDiabetes = assessment.medicalConditions.includes('diabetes');
    const hasWeightGoals = assessment.nutritionGoals.some(goal => goal.includes('weight'));

    if (hasDiabetes) {
      return `Thank you for consulting with me about your diabetes nutrition management. As a registered dietitian, I specialize in helping people with diabetes achieve optimal blood sugar control through personalized nutrition strategies.

**DIABETES NUTRITION MANAGEMENT:**

**KEY PRINCIPLES:**
1. **Carbohydrate Consistency**: Eating similar amounts of carbs at similar times
2. **Portion Control**: Using the plate method (1/2 vegetables, 1/4 lean protein, 1/4 starch)
3. **Timing**: Regular meal and snack timing to match medication
4. **Quality**: Choosing complex carbohydrates and fiber-rich foods

**BLOOD SUGAR FRIENDLY FOODS:**
- **Vegetables**: Non-starchy vegetables (unlimited)
- **Proteins**: Lean meats, fish, eggs, tofu, legumes
- **Carbohydrates**: Whole grains, fruits, dairy (measured portions)
- **Fats**: Healthy fats like avocado, nuts, olive oil (moderate amounts)

**CARBOHYDRATE COUNTING BASICS:**
- 1 carb choice = 15 grams of carbohydrates
- Most people need 3-4 carb choices per meal
- Work with your healthcare team to determine your specific needs

**RECOMMENDED NEXT STEPS:**
${assessment.recommendedApproach.join('\n')}

**IMPORTANT**: Diabetes nutrition is highly individualized. I recommend working with a certified diabetes educator (CDE) and your healthcare team to develop a personalized meal plan that works with your medications, lifestyle, and preferences.

What specific aspects of diabetes nutrition would you like to explore further?`;
    }

    if (hasWeightGoals) {
      const weightGoal = assessment.nutritionGoals.find(goal => goal.includes('weight'));
      return `I'm here to help you achieve your weight management goals through sustainable, evidence-based nutrition strategies.

**SUSTAINABLE WEIGHT MANAGEMENT:**

**FOUNDATIONAL PRINCIPLES:**
1. **Caloric Balance**: Creating appropriate calorie deficit/surplus for your goals
2. **Nutrient Density**: Choosing foods that provide maximum nutrition per calorie
3. **Satiety**: Including protein, fiber, and healthy fats to feel satisfied
4. **Sustainability**: Making changes you can maintain long-term

**EVIDENCE-BASED STRATEGIES:**
- **Portion Awareness**: Using visual cues and measuring tools
- **Meal Planning**: Preparing balanced meals and snacks in advance
- **Mindful Eating**: Paying attention to hunger and fullness cues
- **Hydration**: Drinking adequate water throughout the day

**BALANCED PLATE APPROACH:**
- 1/2 plate: Vegetables and fruits
- 1/4 plate: Lean protein
- 1/4 plate: Whole grains or starchy vegetables
- Small amount: Healthy fats

**RECOMMENDED APPROACH:**
${assessment.recommendedApproach.join('\n')}

**IMPORTANT**: Sustainable weight management is about creating healthy habits, not restrictive dieting. I recommend working with a registered dietitian to develop a personalized plan that fits your lifestyle, preferences, and health needs.

What specific challenges or questions do you have about your nutrition goals?`;
    }

    return `Hello! I'm Dr. Lisa Thompson, a Registered Dietitian Nutritionist specializing in evidence-based nutrition counseling and medical nutrition therapy.

**NUTRITION ASSESSMENT:**
- Primary Focus: ${assessment.primaryFocus.replace('_', ' ').toUpperCase()}
- Intervention Level: ${assessment.interventionLevel.toUpperCase()}
- Identified Goals: ${assessment.nutritionGoals.join(', ') || 'General wellness'}

**MY NUTRITION EXPERTISE INCLUDES:**
- Personalized meal planning and dietary counseling
- Medical nutrition therapy for chronic diseases
- Weight management and metabolic health
- Sports nutrition and performance optimization
- Food allergies and intolerances
- Eating disorder recovery support

**EVIDENCE-BASED NUTRITION PRINCIPLES:**
- Balanced, varied diet with all food groups
- Adequate protein for muscle maintenance and satiety
- Plenty of fruits and vegetables for vitamins, minerals, and fiber
- Whole grains for sustained energy and fiber
- Healthy fats for hormone production and nutrient absorption
- Adequate hydration for optimal body function

**RECOMMENDED APPROACH:**
${assessment.recommendedApproach.join('\n')}

**PERSONALIZED NUTRITION PLANNING:**
Optimal nutrition is highly individual and depends on your health status, goals, preferences, cultural background, and lifestyle. I believe in sustainable changes that enhance your relationship with food while supporting your health goals.

What specific nutrition questions or goals can I help you address today?`;
  }

  /**
   * Get recommended approach based on assessment
   */
  private getRecommendedApproach(focus: string, level: string): string[] {
    const approaches: Record<string, string[]> = {
      diabetes_management: [
        '• Schedule appointment with certified diabetes educator (CDE)',
        '• Learn carbohydrate counting and portion control',
        '• Monitor blood sugar response to different foods',
        '• Coordinate nutrition plan with medication timing',
        '• Regular follow-up for plan adjustments'
      ],
      cardiovascular_nutrition: [
        '• Adopt DASH or Mediterranean eating pattern',
        '• Reduce sodium intake to <2300mg daily',
        '• Increase omega-3 fatty acids from fish and plants',
        '• Focus on soluble fiber for cholesterol management',
        '• Coordinate with cardiologist for comprehensive care'
      ],
      weight_management: [
        '• Set realistic, sustainable weight goals (1-2 lbs/week)',
        '• Focus on behavior change and habit formation',
        '• Include regular physical activity as appropriate',
        '• Track food intake and hunger/fullness cues',
        '• Regular monitoring and plan adjustments'
      ],
      sports_nutrition: [
        '• Optimize pre/post workout nutrition timing',
        '• Ensure adequate protein for muscle recovery',
        '• Plan carbohydrate intake around training',
        '• Address hydration and electrolyte needs',
        '• Consider performance testing and monitoring'
      ],
      general_wellness: [
        '• Focus on balanced, varied eating pattern',
        '• Include all food groups in appropriate portions',
        '• Emphasize whole, minimally processed foods',
        '• Stay adequately hydrated',
        '• Consider individual preferences and lifestyle'
      ]
    };

    return approaches[focus] || approaches.general_wellness || [];
  }

  /**
   * Analyze need for specialist collaboration
   */
  private analyzeForSpecialistCollaboration(request: AgentRequest): AgentHandoffSuggestion[] {
    const message = request.userMessage.toLowerCase();
    const suggestions: AgentHandoffSuggestion[] = [];

    // Cardiology for heart-related nutrition
    if (message.includes('heart') || message.includes('cholesterol') || message.includes('blood pressure')) {
      suggestions.push({
        targetAgentRole: 'cardiologist',
        reason: 'Cardiovascular nutrition requires coordination with cardiology care',
        urgency: 'medium',
        contextToTransfer: 'Patient with cardiovascular nutrition needs - integrated care approach'
      });
    }

    // Mental health for eating disorders
    if (message.includes('eating disorder') || message.includes('binge') || message.includes('restrict')) {
      suggestions.push({
        targetAgentRole: 'psychiatrist',
        reason: 'Eating disorder treatment requires mental health support alongside nutrition therapy',
        urgency: 'high',
        contextToTransfer: 'Patient with eating disorder concerns - multidisciplinary treatment needed'
      });
    }

    // General practitioner for medical nutrition therapy
    if (message.includes('diabetes') || message.includes('kidney') || message.includes('medical condition')) {
      suggestions.push({
        targetAgentRole: 'general_practitioner',
        reason: 'Medical nutrition therapy requires coordination with primary care',
        urgency: 'medium',
        contextToTransfer: 'Patient requiring medical nutrition therapy - physician coordination needed'
      });
    }

    return suggestions;
  }

  /**
   * Calculate confidence for nutrition consultations
   */
  private calculateNutritionConfidence(request: AgentRequest): number {
    let confidence = 0.9; // High base confidence for nutrition

    const message = request.userMessage.toLowerCase();

    // High confidence for nutrition topics
    const nutritionKeywords = [
      'diet', 'nutrition', 'food', 'eating', 'weight', 'calories',
      'diabetes', 'cholesterol', 'meal plan', 'healthy eating'
    ];

    if (nutritionKeywords.some(keyword => message.includes(keyword))) {
      confidence = 0.95;
    }

    // Lower confidence for non-nutrition topics
    const nonNutritionKeywords = [
      'surgery', 'medication', 'chest pain', 'emergency', 'acute'
    ];

    if (nonNutritionKeywords.some(keyword => message.includes(keyword))) {
      confidence -= 0.3;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Generate nutrition follow-up actions
   */
  private generateNutritionFollowUp(request: AgentRequest, assessment: NutritionAssessment) {
    const actions = [];

    // Always recommend nutrition follow-up
    actions.push({
      type: 'schedule_appointment' as const,
      description: 'Schedule follow-up with registered dietitian for personalized nutrition plan',
      timeframe: this.getFollowUpTimeframe(assessment.interventionLevel),
      priority: assessment.interventionLevel === 'intensive' ? 'high' as const : 'medium' as const
    });

    // Intensive cases need more frequent monitoring
    if (assessment.interventionLevel === 'intensive') {
      actions.push({
        type: 'test_results' as const,
        description: 'Monitor relevant lab values (blood sugar, lipids, etc.) as recommended',
        timeframe: 'Within 4-6 weeks',
        priority: 'high' as const
      });
    }

    return actions;
  }

  /**
   * Get follow-up timeframe based on intervention level
   */
  private getFollowUpTimeframe(level: string): string {
    switch (level) {
      case 'intensive': return 'Within 1-2 weeks';
      case 'moderate': return 'Within 2-4 weeks';
      default: return 'Within 4-6 weeks';
    }
  }

  /**
   * Enhanced confidence scoring for nutrition requests
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = super.getConfidenceScore(request);

    // Nutritionist is highly confident for nutrition issues
    const nutritionKeywords = [
      'nutrition', 'diet', 'food', 'eating', 'weight', 'calories',
      'meal', 'diabetes', 'cholesterol', 'healthy eating'
    ];

    const message = request.userMessage.toLowerCase();
    if (nutritionKeywords.some(keyword => message.includes(keyword))) {
      score += 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }
}

interface NutritionAssessment {
  primaryFocus: string;
  interventionLevel: 'basic' | 'moderate' | 'intensive';
  nutritionGoals: string[];
  medicalConditions: string[];
  recommendedApproach: string[];
}

export default NutritionistAgent;
