#!/usr/bin/env node
/**
 * CONVERSATION MEMORY BREAKING CHANGE FIX SCRIPT
 * 
 * This script identifies and fixes all usages of the old conversation memory
 * property names that were changed in aiOrchestrator.ts lines 732-736.
 */

import fs from 'fs';
import path from 'path';

// Property mapping from old to new names
const PROPERTY_MAPPING = {
  'message_count': 'total_messages',
  'created_at': 'last_activity', // Closest semantic match
  'updated_at': 'last_activity'  // Closest semantic match
};

// Directories to search (excluding node_modules, dist, etc.)
const SEARCH_DIRS = [
  'src',
  'api',
  'scripts'
];

// File extensions to check
const FILE_EXTENSIONS = ['.ts', '.js', '.tsx', '.jsx', '.json'];

let fixedFiles = [];
let totalIssues = 0;

function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return FILE_EXTENSIONS.includes(ext) && 
         !filePath.includes('node_modules') &&
         !filePath.includes('dist') &&
         !filePath.includes('.git');
}

function findAndFixIssues(content, filePath) {
  let hasChanges = false;
  let issueCount = 0;
  
  // Fix property access patterns
  Object.entries(PROPERTY_MAPPING).forEach(([oldProp, newProp]) => {
    // Match patterns like: .message_count, context.message_count, obj["message_count"]
    const patterns = [
      new RegExp(`\\.${oldProp}\\b`, 'g'),
      new RegExp(`\\["${oldProp}"\\]`, 'g'),
      new RegExp(`\\['${oldProp}'\\]`, 'g'),
      new RegExp(`${oldProp}:`, 'g')
    ];
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        issueCount += matches.length;
        hasChanges = true;
        console.log(`📍 Found ${matches.length} usage(s) of '${oldProp}' in ${filePath}`);
        
        // Apply the fix
        if (pattern.source.includes('\\.')) {
          content = content.replace(pattern, `.${newProp}`);
        } else if (pattern.source.includes('\\["')) {
          content = content.replace(pattern, `["${newProp}"]`);
        } else if (pattern.source.includes("\\'")) {
          content = content.replace(pattern, `['${newProp}']`);
        } else if (pattern.source.includes(':')) {
          content = content.replace(pattern, `${newProp}:`);
        }
      }
    });
  });
  
  return { content, hasChanges, issueCount };
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      processDirectory(fullPath);
    } else if (stats.isFile() && shouldProcessFile(fullPath)) {
      processFile(fullPath);
    }
  });
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = findAndFixIssues(content, filePath);
    
    if (result.hasChanges) {
      // Backup original file
      const backupPath = `${filePath}.backup`;
      fs.writeFileSync(backupPath, content);
      
      // Write fixed content
      fs.writeFileSync(filePath, result.content);
      
      fixedFiles.push({
        path: filePath,
        issues: result.issueCount,
        backup: backupPath
      });
      
      totalIssues += result.issueCount;
      console.log(`✅ Fixed ${result.issueCount} issue(s) in ${filePath}`);
      console.log(`📁 Backup created: ${backupPath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

// Main execution
console.log('🔍 Scanning for conversation memory breaking changes...');
console.log('Property mappings:');
Object.entries(PROPERTY_MAPPING).forEach(([old, newProp]) => {
  console.log(`  ${old} → ${newProp}`);
});
console.log('');

SEARCH_DIRS.forEach(dir => {
  console.log(`📂 Processing directory: ${dir}`);
  processDirectory(dir);
});

// Summary
console.log('\n🎯 SUMMARY:');
console.log(`Total files fixed: ${fixedFiles.length}`);
console.log(`Total issues resolved: ${totalIssues}`);

if (fixedFiles.length > 0) {
  console.log('\n📋 Fixed files:');
  fixedFiles.forEach(file => {
    console.log(`  ${file.path} (${file.issues} issues)`);
  });
  
  console.log('\n⚠️  IMPORTANT:');
  console.log('1. Review all changes before committing');
  console.log('2. Run tests to verify functionality');
  console.log('3. Backup files are available if rollback is needed');
  console.log('4. Update any documentation that references old property names');
} else {
  console.log('✨ No breaking changes found! The codebase is already using the new property names.');
}

process.exit(0);