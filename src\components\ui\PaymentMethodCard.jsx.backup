import React from 'react';
import { CreditCard, Trash2, CheckCircle } from 'lucide-react';
import Button from './Button';

const PaymentMethodCard = ({ 
  paymentMethod, 
  isDefault = false, 
  onSetDefault, 
  onDelete, 
  className = '' 
}) => {
  const getCardIcon = (brand) => {
    // You can add specific brand icons here
    switch (brand?.toLowerCase()) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'verve':
        return '💳';
      default:
        return '💳';
    }
  };

  const formatCardNumber = (lastFour) => {
    return `•••• •••• •••• ${lastFour}`;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            {paymentMethod?.card_type === 'card' ? (
              <CreditCard size={20} className="text-gray-600" />
            ) : (
              <span className="text-lg">{getCardIcon(paymentMethod?.brand)}</span>
            )}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-gray-900">
                {paymentMethod?.brand?.charAt(0)?.toUpperCase() + paymentMethod?.brand?.slice(1) || 'Card'}
              </p>
              {isDefault && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <CheckCircle size={12} className="mr-1" />
                  Default
                </span>
              )}
            </div>
            
            <p className="text-sm text-gray-600 mt-1">
              {formatCardNumber(paymentMethod?.last_four)}
            </p>
            
            <p className="text-xs text-gray-500 mt-1">
              Expires {paymentMethod?.exp_month}/{paymentMethod?.exp_year}
            </p>
            
            {paymentMethod?.bank && (
              <p className="text-xs text-gray-500">
                {paymentMethod.bank}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {!isDefault && onSetDefault && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSetDefault(paymentMethod.id)}
              className="text-xs"
            >
              Set Default
            </Button>
          )}
          
          {onDelete && (
            <button
              onClick={() => onDelete(paymentMethod.id)}
              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete payment method"
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      </div>

      {/* Card Status Indicator */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs">
          <span className={`inline-flex items-center px-2 py-1 rounded-full ${
            paymentMethod?.is_active 
              ? 'bg-green-100 text-green-800' :'bg-red-100 text-red-800'
          }`}>
            {paymentMethod?.is_active ? 'Active' : 'Inactive'}
          </span>
          
          <span className="text-gray-500">
            Added {new Date(paymentMethod?.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethodCard;