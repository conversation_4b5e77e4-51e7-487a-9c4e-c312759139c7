import React, { useState } from 'react';
import { Download, Eye, Filter, CreditCard, CheckCircle, XCircle, Clock, RefreshCw } from 'lucide-react';
import Button from './Button';

const PaymentHistory = ({ transactions = [], onDownloadReceipt, className = '' }) => {
  const [filter, setFilter] = useState('all'); // 'all', 'completed', 'pending', 'failed'
  const [sortBy, setSortBy] = useState('date'); // 'date', 'amount', 'status'
  const [sortOrder, setSortOrder] = useState('desc'); // 'asc', 'desc'

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'failed':
        return <XCircle size={16} className="text-red-600" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-600" />;
      case 'refunded':
        return <RefreshCw size={16} className="text-blue-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800',
      refunded: 'bg-blue-100 text-blue-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        statusConfig[status?.toLowerCase()] || statusConfig.cancelled
      }`}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </span>
    );
  };

  const formatAmount = (amount, currency) => {
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency || 'NGN',
      minimumFractionDigits: 2
    });
    return formatter.format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPaymentType = (transaction) => {
    if (transaction.subscription_id) return 'Subscription';
    if (transaction.consultation_session_id) return 'Consultation';
    return 'Payment';
  };

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true;
    return transaction.status === filter;
  });

  // Sort transactions
  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'amount':
        aValue = parseFloat(a.amount);
        bValue = parseFloat(b.amount);
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'date':
      default:
        aValue = new Date(a.created_at);
        bValue = new Date(b.created_at);
        break;
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 sm:mb-0">
            Payment History
          </h3>
          
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-3">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Transactions</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>
            
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [newSortBy, newSortOrder] = e.target.value.split('-');
                setSortBy(newSortBy);
                setSortOrder(newSortOrder);
              }}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="date-desc">Newest First</option>
              <option value="date-asc">Oldest First</option>
              <option value="amount-desc">Highest Amount</option>
              <option value="amount-asc">Lowest Amount</option>
              <option value="status-asc">Status A-Z</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="divide-y divide-gray-200">
        {sortedTransactions.length === 0 ? (
          <div className="p-8 text-center">
            <CreditCard size={48} className="mx-auto text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No transactions found
            </h4>
            <p className="text-gray-600">
              {filter === 'all' ? "You haven't made any payments yet" 
                : `No ${filter} transactions found`
              }
            </p>
          </div>
        ) : (
          sortedTransactions.map((transaction) => (
            <div key={transaction.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <CreditCard size={20} className="text-blue-600" />
                  </div>
                  
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">
                        {getPaymentType(transaction)}
                      </h4>
                      {getStatusBadge(transaction.status)}
                    </div>
                    
                    <p className="text-sm text-gray-600">
                      {formatDate(transaction.created_at)}
                    </p>
                    
                    <p className="text-xs text-gray-500 mt-1">
                      Ref: {transaction.paystack_reference}
                    </p>
                    
                    {transaction.payment_method && (
                      <p className="text-xs text-gray-500 capitalize">
                        via {transaction.payment_method}
                      </p>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    {formatAmount(transaction.amount, transaction.currency)}
                  </p>
                  
                  {transaction.status === 'completed' && onDownloadReceipt && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDownloadReceipt(transaction.id)}
                      className="mt-2"
                    >
                      <Download size={14} className="mr-1" />
                      Receipt
                    </Button>
                  )}
                </div>
              </div>

              {/* Transaction Details (Expandable) */}
              {transaction.gateway_response && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <details className="group">
                    <summary className="flex items-center justify-between cursor-pointer text-sm text-gray-600 hover:text-gray-900">
                      <span>View Details</span>
                      <Eye size={16} className="group-open:rotate-180 transition-transform" />
                    </summary>
                    
                    <div className="mt-3 text-xs text-gray-600 bg-gray-50 rounded-lg p-3">
                      <div className="grid grid-cols-2 gap-2">
                        {transaction.paystack_transaction_id && (
                          <>
                            <dt className="font-medium">Transaction ID:</dt>
                            <dd>{transaction.paystack_transaction_id}</dd>
                          </>
                        )}
                        
                        {transaction.payment_date && (
                          <>
                            <dt className="font-medium">Paid At:</dt>
                            <dd>{formatDate(transaction.payment_date)}</dd>
                          </>
                        )}
                        
                        {transaction.gateway_response?.channel && (
                          <>
                            <dt className="font-medium">Channel:</dt>
                            <dd className="capitalize">{transaction.gateway_response.channel}</dd>
                          </>
                        )}
                        
                        {transaction.gateway_response?.bank && (
                          <>
                            <dt className="font-medium">Bank:</dt>
                            <dd>{transaction.gateway_response.bank}</dd>
                          </>
                        )}
                      </div>
                    </div>
                  </details>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Summary Footer */}
      {sortedTransactions.length > 0 && (
        <div className="p-6 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">
              Showing {sortedTransactions.length} of {transactions.length} transactions
            </span>
            <span className="font-medium text-gray-900">
              Total: {formatAmount(
                sortedTransactions.reduce((sum, t) => sum + parseFloat(t.amount || 0), 0),
                sortedTransactions[0]?.currency || 'NGN'
              )}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;