/**
 * AUTHENTICATION AND RBAC TYPE DEFINITIONS
 * 
 * This file contains TypeScript type definitions for authentication,
 * authorization, and role-based access control (RBAC) systems.
 * 
 * SECURITY REQUIREMENTS:
 * - Strict typing for user roles and permissions
 * - Type-safe session management
 * - Clear separation of authentication and authorization
 * - Emergency access protocols
 */

import { UserRole } from './medical';

// Re-export UserRole for convenience
export type { UserRole } from './medical';

// Resource types for RBAC
export type ResourceType = 
  | 'medical_data'
  | 'patient_records'
  | 'consultations'
  | 'emergency_protocols'
  | 'security_logs'
  | 'audit_logs'
  | 'system_settings'
  | 'user_management'
  | 'admin_dashboard'
  | 'medical_conditions'
  | 'medications'
  | 'patient_profiles';

// Action types for RBAC  
export type Action = 
  | 'read'
  | 'write'
  | 'update'
  | 'delete'
  | 'execute'
  | 'manage'
  | 'create'
  | 'export'
  | 'share'
  | 'emergency_access';

// Authentication types
export interface User {
  readonly id: string;
  readonly email: string;
  readonly email_confirmed_at?: string;
  readonly phone?: string;
  readonly created_at: string;
  readonly updated_at?: string;
  readonly last_sign_in_at?: string;
  readonly user_metadata?: UserMetadata;
  readonly app_metadata?: AppMetadata;
}

export interface UserMetadata {
  readonly full_name?: string;
  readonly role?: UserRole;
  readonly avatar_url?: string;
  readonly phone_verified?: boolean;
  readonly emergency_contact?: EmergencyContact;
}

export interface AppMetadata {
  readonly provider?: string;
  readonly providers?: string[];
  readonly roles?: UserRole[];
  readonly permissions?: Permission[];
  readonly last_password_change?: string;
  readonly account_locked?: boolean;
  readonly failed_login_attempts?: number;
}

export interface EmergencyContact {
  readonly name: string;
  readonly phone: string;
  readonly relationship: string;
}

// Session types
export interface Session {
  readonly access_token: string;
  readonly refresh_token: string;
  readonly expires_in: number;
  readonly expires_at?: number;
  readonly token_type: 'bearer';
  readonly user: User;
}

export interface AuthResponse {
  readonly success: boolean;
  readonly data?: {
    readonly user: User;
    readonly session: Session;
  };
  readonly error?: string;
}

// RBAC Permission system - types defined above

export interface Permission {
  readonly resource: ResourceType;
  readonly action: Action;
  readonly conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  readonly field: string;
  readonly operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  readonly value: unknown;
}

// Role definitions with permissions
export interface RoleDefinition {
  readonly role: UserRole;
  readonly name: string;
  readonly description: string;
  readonly permissions: Permission[];
  readonly inherits_from?: UserRole[];
  readonly emergency_override?: boolean;
}

// Predefined role configurations
export const ROLE_DEFINITIONS: Record<UserRole, RoleDefinition> = {
  patient: {
    role: 'patient',
    name: 'Patient',
    description: 'Individual receiving medical care',
    permissions: [
      { resource: 'medical_conditions', action: 'create' },
      { resource: 'medical_conditions', action: 'read', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medical_conditions', action: 'update', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medical_conditions', action: 'delete', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'create' },
      { resource: 'medications', action: 'read', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'update', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'delete', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'read', conditions: [{ field: 'patient_id', operator: 'equals', value: 'self' }] },
      { resource: 'patient_profiles', action: 'read', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'patient_profiles', action: 'update', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] }
    ],
    emergency_override: false
  },
  healthcare_provider: {
    role: 'healthcare_provider',
    name: 'Healthcare Provider',
    description: 'Medical professional providing care',
    permissions: [
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medical_conditions', action: 'create' },
      { resource: 'medical_conditions', action: 'update' },
      { resource: 'medications', action: 'read' },
      { resource: 'medications', action: 'create' },
      { resource: 'medications', action: 'update' },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'update' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'patient_profiles', action: 'update' },
      { resource: 'audit_logs', action: 'read', conditions: [{ field: 'resource_type', operator: 'in', value: ['medical_conditions', 'medications', 'consultations'] }] }
    ],
    inherits_from: ['patient'],
    emergency_override: true
  },
  admin: {
    role: 'admin',
    name: 'Administrator',
    description: 'System administrator with full access',
    permissions: [
      { resource: 'medical_conditions', action: 'create' },
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medical_conditions', action: 'update' },
      { resource: 'medical_conditions', action: 'delete' },
      { resource: 'medications', action: 'create' },
      { resource: 'medications', action: 'read' },
      { resource: 'medications', action: 'update' },
      { resource: 'medications', action: 'delete' },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'update' },
      { resource: 'consultations', action: 'delete' },
      { resource: 'patient_profiles', action: 'create' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'patient_profiles', action: 'update' },
      { resource: 'patient_profiles', action: 'delete' },
      { resource: 'audit_logs', action: 'read' },
      { resource: 'audit_logs', action: 'export' },
      { resource: 'system_settings', action: 'read' },
      { resource: 'system_settings', action: 'update' },
      { resource: 'user_management', action: 'create' },
      { resource: 'user_management', action: 'read' },
      { resource: 'user_management', action: 'update' },
      { resource: 'user_management', action: 'delete' }
    ],
    inherits_from: ['healthcare_provider'],
    emergency_override: true
  },
  emergency_responder: {
    role: 'emergency_responder',
    name: 'Emergency Responder',
    description: 'Emergency medical personnel with override access',
    permissions: [
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medications', action: 'read' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'create' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'emergency_protocols', action: 'emergency_access' }
    ],
    emergency_override: true
  }
};

// Authorization context
export interface AuthContext {
  readonly user: User | null;
  readonly session: Session | null;
  readonly loading: boolean;
  readonly permissions: Permission[];
  readonly hasPermission: (resource: ResourceType, action: Action, context?: Record<string, unknown>) => boolean;
  readonly isEmergencyOverride: boolean;
}

// Emergency access types
export interface EmergencyAccessRequest {
  readonly id: string;
  readonly requester_id: string;
  readonly patient_id: string;
  readonly reason: string;
  readonly urgency: 'high' | 'critical';
  readonly requested_at: string;
  readonly approved_at?: string;
  readonly approved_by?: string;
  readonly expires_at: string;
  readonly status: 'pending' | 'approved' | 'denied' | 'expired';
}

export interface EmergencyAccessLog {
  readonly id: string;
  readonly access_request_id: string;
  readonly user_id: string;
  readonly patient_id: string;
  readonly resources_accessed: ResourceType[];
  readonly actions_performed: Action[];
  readonly access_duration_minutes: number;
  readonly justification: string;
  readonly created_at: string;
}

// Authentication service interface
// Extended types for optimized auth context
export interface UserProfile {
  readonly id: string;
  readonly user_id: string;
  readonly full_name: string;
  readonly first_name?: string;
  readonly last_name?: string;
  readonly email: string;
  readonly phone_number?: string;
  readonly avatar_url?: string;
  readonly role: UserRole;
  readonly permissions: Permission[];
  readonly medical_license?: string;
  readonly specialization?: string[];
  readonly department?: string;
  readonly organization?: string;
  readonly emergency_contact: EmergencyContact;
  readonly preferences: UserPreferences;
  readonly security_settings: SecuritySettings;
  readonly created_at: string;
  readonly updated_at: string;
  readonly last_login_at?: string;
  readonly is_active: boolean;
  readonly verification_status: 'unverified' | 'email_verified' | 'phone_verified' | 'fully_verified';
}

export interface UserPreferences {
  readonly theme: 'light' | 'dark' | 'auto';
  readonly language: string;
  readonly timezone: string;
  readonly date_format: string;
  readonly time_format: '12h' | '24h';
  readonly measurement_units: 'metric' | 'imperial';
  readonly dashboard_layout: string;
  readonly default_view: string;
  readonly auto_save: boolean;
  readonly offline_mode: boolean;
  readonly data_sync_frequency: 'real-time' | 'hourly' | 'daily';
  readonly notification_preferences: NotificationPreferences;
  readonly accessibility_settings: AccessibilitySettings;
}

export interface NotificationPreferences {
  readonly email_notifications: boolean;
  readonly sms_notifications: boolean;
  readonly push_notifications: boolean;
  readonly emergency_notifications: boolean;
  readonly appointment_reminders: boolean;
  readonly medication_reminders: boolean;
  readonly lab_result_notifications: boolean;
  readonly marketing_communications: boolean;
  readonly frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
  readonly quiet_hours?: {
    readonly start: string;
    readonly end: string;
  };
}

export interface AccessibilitySettings {
  readonly high_contrast: boolean;
  readonly large_text: boolean;
  readonly screen_reader_support: boolean;
  readonly keyboard_navigation: boolean;
  readonly voice_commands: boolean;
  readonly reduced_motion: boolean;
  readonly color_blind_support: boolean;
  readonly font_size_multiplier: number;
}

export interface SecuritySettings {
  readonly two_factor_enabled: boolean;
  readonly two_factor_method: 'sms' | 'email' | 'authenticator' | 'hardware_key';
  readonly session_timeout: number;
  readonly password_last_changed: string;
  readonly login_notifications: boolean;
  readonly device_tracking: boolean;
  readonly ip_whitelist?: string[];
  readonly security_questions_set: boolean;
  readonly biometric_enabled: boolean;
  readonly emergency_bypass_enabled: boolean;
}

export interface AuthError {
  readonly code: string;
  readonly message: string;
  readonly details?: any;
  readonly timestamp: string;
  readonly user_id?: string;
  readonly session_id?: string;
  readonly ip_address?: string;
  readonly user_agent?: string;
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly category: 'authentication' | 'authorization' | 'session' | 'security';
}

export interface AuthService {
  signIn(email: string, password: string): Promise<AuthResponse>;
  signUp(email: string, password: string, userData?: Partial<UserMetadata>): Promise<AuthResponse>;
  signOut(): Promise<{ success: boolean; error?: string }>;
  getCurrentUser(): Promise<User | null>;
  getSession(): Promise<{ success: boolean; data?: { session: Session } }>;
  getUserProfile(userId: string): Promise<{ success: boolean; data?: UserProfile; error?: string }>;
  getUserPermissions(userId: string): Promise<{ success: boolean; data?: Permission[]; error?: string }>;
  updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<{ success: boolean; data?: UserProfile; error?: string }>;
  refreshSession(): Promise<Session | null>;
  updateUserMetadata(updates: Partial<UserMetadata>): Promise<{ success: boolean; error?: string }>;
  changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }>;
  resetPassword(email: string): Promise<{ success: boolean; error?: string }>;
  requestEmergencyAccess(userId: string, justification: string): Promise<{ success: boolean; error?: string }>;
  onAuthStateChange(callback: (event: string, session: Session | null) => void): { data: { subscription: unknown } };
}

// RBAC service interface
export interface RBACService {
  hasPermission(user: User, resource: ResourceType, action: Action, context?: Record<string, unknown>): boolean;
  getUserPermissions(user: User): Permission[];
  requestEmergencyAccess(patientId: string, reason: string, urgency: 'high' | 'critical'): Promise<EmergencyAccessRequest>;
  approveEmergencyAccess(requestId: string, approverId: string): Promise<void>;
  logEmergencyAccess(accessRequestId: string, resourcesAccessed: ResourceType[], actionsPerformed: Action[], justification: string): Promise<void>;
  checkEmergencyOverride(user: User): boolean;
}
