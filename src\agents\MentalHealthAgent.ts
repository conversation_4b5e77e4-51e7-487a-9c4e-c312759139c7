/**
 * MENTAL HEALTH AGENT (PSYCHIATRIST)
 * 
 * Specialized AI agent for mental health support, psychiatric consultation, and psychological wellness.
 * Provides expert guidance on mental health conditions, crisis intervention, and therapeutic support.
 * 
 * SPECIALIZATION AREAS:
 * - Depression and mood disorders assessment and support
 * - Anxiety disorders and panic management
 * - Crisis intervention and suicide risk assessment
 * - Stress management and coping strategies
 * - PTSD and trauma-informed care
 * - Substance abuse and addiction support
 * - Eating disorder recognition and referral
 * - Sleep disorders and mental health
 */

import { BaseAgent } from './BaseAgent';
import type { 
  AgentRequest, 
  AgentResponse, 
  AgentRole, 
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class MentalHealthAgent extends BaseAgent {
  constructor(memoryManager: MemoryManager) {
    const id = 'mental-health-agent-001';
    const name = 'Dr. <PERSON>';
    const role: AgentRole = 'psychiatrist';
    const capabilities: AgentCapability[] = [
      'specialist_consultation',
      'emergency_response',
      'patient_education',
      'treatment_planning',
      'chronic_disease_management'
    ];

    const systemPrompt = `You are Dr. <PERSON>, a board-certified Psychiatrist with 18 years of experience in mental health care, crisis intervention, and therapeutic support. You specialize in comprehensive mental health assessment and evidence-based treatment approaches.

CORE EXPERTISE:
- Depression and mood disorders (major depression, bipolar disorder, seasonal affective disorder)
- Anxiety disorders (generalized anxiety, panic disorder, social anxiety, phobias)
- Trauma and PTSD (post-traumatic stress disorder, complex trauma)
- Crisis intervention and suicide risk assessment
- Substance use disorders and dual diagnosis
- Eating disorders and body image issues
- Sleep disorders and their mental health impact
- Stress management and resilience building

ASSESSMENT APPROACH:
- Comprehensive mental health screening using validated tools
- Risk assessment for self-harm and suicide
- Trauma-informed care principles
- Cultural sensitivity and awareness
- Collaborative treatment planning
- Integration with medical care for holistic health

CRISIS INTERVENTION PROTOCOLS:
- Immediate suicide risk assessment using standardized protocols
- Safety planning and crisis de-escalation
- Emergency contact and resource mobilization
- Coordination with emergency services when necessary
- Follow-up care planning for crisis situations

COMMUNICATION STYLE:
- Empathetic, non-judgmental, and validating approach
- Active listening and reflective responses
- Strength-based perspective focusing on resilience
- Clear, supportive language that reduces stigma
- Collaborative and patient-centered communication
- Respect for patient autonomy and dignity

THERAPEUTIC APPROACHES:
- Cognitive Behavioral Therapy (CBT) principles
- Mindfulness and stress reduction techniques
- Psychoeducation about mental health conditions
- Coping skills development and practice
- Behavioral activation for depression
- Exposure therapy concepts for anxiety

SAFETY PRIORITIES:
- Immediate identification of suicidal or homicidal ideation
- Assessment of self-harm behaviors and risk factors
- Recognition of psychiatric emergencies requiring immediate intervention
- Substance abuse screening and safety considerations
- Coordination with emergency services and crisis teams

COLLABORATION FOCUS:
- Work closely with primary care for integrated mental health care
- Coordinate with emergency services for crisis situations
- Partner with nutritionists for eating disorder treatment
- Collaborate with cardiologists for anxiety and cardiac symptoms
- Engage with social services for comprehensive support

ETHICAL CONSIDERATIONS:
- Maintain strict confidentiality while ensuring safety
- Respect patient autonomy and informed consent
- Cultural humility and awareness of diverse backgrounds
- Recognition of scope of practice limitations
- Appropriate referrals to specialized mental health services

Remember: Mental health is as important as physical health. Your role is to provide immediate support, assess risk, and guide patients toward appropriate mental health resources. Always prioritize safety and encourage professional mental health treatment when indicated.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager);
  }

  /**
   * Handle mental health consultation requests
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🧠 Mental Health Agent processing request for session: ${request.sessionId}`);

      // Immediate crisis and suicide risk assessment
      const emergencyFlags = this.detectMentalHealthCrises(request.userMessage);
      
      // If mental health crisis detected, prioritize crisis intervention
      if (emergencyFlags.some(flag => flag.severity === 'critical')) {
        return this.handleMentalHealthCrisis(request, emergencyFlags);
      }

      // Assess mental health needs and risk factors
      const mentalHealthAssessment = this.assessMentalHealthNeeds(request);

      // Generate specialized mental health response
      const response = await this.generateMentalHealthResponse(request, mentalHealthAssessment);

      // Determine if other specialists needed
      const handoffSuggestions = this.analyzeForSpecialistCollaboration(request);

      // Calculate confidence based on mental health complexity
      const confidence = this.calculateMentalHealthConfidence(request);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, confidence, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence,
        reasoning: 'Mental health assessment completed with evidence-based therapeutic support',
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: this.generateMentalHealthFollowUp(request, mentalHealthAssessment),
        metadata: {
          responseTime,
          assessmentType: 'mental_health_consultation',
          riskLevel: mentalHealthAssessment.riskLevel,
          primaryConcern: mentalHealthAssessment.primaryConcern
        }
      };

    } catch (error) {
      console.error('❌ Mental Health Agent error:', error);
      
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I apologize, but I'm experiencing technical difficulties. If you're having thoughts of self-harm or suicide, please contact emergency services (988 Suicide & Crisis Lifeline) or go to your nearest emergency room immediately.",
        confidence: 0.1,
        reasoning: 'Technical error during mental health consultation',
        emergencyFlags: [{
          type: 'mental_health_crisis',
          severity: 'medium',
          description: 'System error - recommend direct mental health consultation',
          recommendedAction: 'Contact mental health professional or crisis hotline',
          timeToResponse: 1000
        }]
      };
    }
  }

  /**
   * Detect mental health crises with high sensitivity
   */
  private detectMentalHealthCrises(message: string): EmergencyFlag[] {
    const emergencyFlags: EmergencyFlag[] = [];
    const lowerMessage = message.toLowerCase();

    // Critical suicide risk indicators
    const suicideRiskKeywords = [
      'want to die', 'kill myself', 'end my life', 'suicide', 'suicidal',
      'not worth living', 'better off dead', 'end it all', 'take my own life'
    ];

    // Self-harm indicators
    const selfHarmKeywords = [
      'cutting myself', 'self harm', 'hurting myself', 'self injury',
      'want to hurt myself', 'cutting', 'burning myself'
    ];

    // Severe mental health crisis indicators
    const crisisKeywords = [
      'psychotic', 'hearing voices', 'seeing things', 'paranoid',
      'losing my mind', 'going crazy', 'can\'t cope', 'breakdown'
    ];

    // Check for suicide risk (highest priority)
    suicideRiskKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        emergencyFlags.push({
          type: 'mental_health_crisis',
          severity: 'critical',
          description: `Suicide risk indicator detected: ${keyword}`,
          recommendedAction: 'IMMEDIATE crisis intervention - call 988 Suicide & Crisis Lifeline or emergency services',
          timeToResponse: 500 // 0.5 seconds for suicide risk
        });
      }
    });

    // Check for self-harm (high priority)
    if (emergencyFlags.length === 0) { // Only if no suicide risk detected
      selfHarmKeywords.forEach(keyword => {
        if (lowerMessage.includes(keyword)) {
          emergencyFlags.push({
            type: 'mental_health_crisis',
            severity: 'high',
            description: `Self-harm indicator detected: ${keyword}`,
            recommendedAction: 'Urgent mental health intervention needed',
            timeToResponse: 1000
          });
        }
      });
    }

    // Check for mental health crisis
    if (emergencyFlags.length === 0) { // Only if no higher risk detected
      crisisKeywords.forEach(keyword => {
        if (lowerMessage.includes(keyword)) {
          emergencyFlags.push({
            type: 'mental_health_crisis',
            severity: 'high',
            description: `Mental health crisis indicator: ${keyword}`,
            recommendedAction: 'Immediate mental health support recommended',
            timeToResponse: 1500
          });
        }
      });
    }

    return emergencyFlags;
  }

  /**
   * Handle mental health crisis situations
   */
  private async handleMentalHealthCrisis(request: AgentRequest, emergencyFlags: EmergencyFlag[]): Promise<AgentResponse> {
    console.log('🚨 Mental Health Agent handling crisis situation');

    const crisisResponse = `🚨 MENTAL HEALTH CRISIS SUPPORT 🚨

I'm here to help you through this difficult moment. Your safety and wellbeing are my top priority.

**IMMEDIATE CRISIS RESOURCES:**
📞 **988 Suicide & Crisis Lifeline**: Call or text 988 (24/7, free, confidential)
📞 **Crisis Text Line**: Text HOME to 741741
📞 **Emergency Services**: 911 if in immediate danger

**YOU ARE NOT ALONE:**
- These feelings can be overwhelming, but they are temporary
- Professional help is available and effective
- Many people have felt this way and found relief
- Your life has value and meaning

**IMMEDIATE SAFETY STEPS:**
1. **Stay Safe**: Remove any means of self-harm from your immediate area
2. **Reach Out**: Contact a trusted friend, family member, or crisis counselor
3. **Stay Connected**: Don't isolate yourself - stay with someone if possible
4. **Seek Help**: Go to your nearest emergency room if you feel unsafe

**COPING STRATEGIES FOR RIGHT NOW:**
- Take slow, deep breaths
- Use grounding techniques (5 things you can see, 4 you can hear, 3 you can touch)
- Listen to calming music or sounds
- Hold ice cubes or splash cold water on your face

**REMEMBER:**
- Crisis feelings are temporary, even when they feel permanent
- Professional help can provide relief and hope
- You deserve support and care
- Taking the step to reach out shows incredible strength

Please reach out to one of the crisis resources above. A trained counselor can provide immediate support and help you develop a safety plan.

Are you in a safe place right now? Can you reach out to someone for support?`;

    return {
      agentId: this.id,
      agentName: this.name,
      content: crisisResponse,
      confidence: 0.98,
      reasoning: 'Mental health crisis detected - immediate crisis intervention provided',
      emergencyFlags,
      suggestedHandoffs: [{
        targetAgentRole: 'emergency',
        reason: 'Mental health crisis requiring emergency support coordination',
        urgency: 'critical',
        contextToTransfer: `Mental health crisis: ${emergencyFlags.map(f => f.description).join(', ')}`,
        patientConsent: false // Crisis override
      }],
      metadata: {
        crisisDetected: true,
        mentalHealthCrisis: true,
        responseTime: Date.now(),
        priorityLevel: 'critical'
      }
    };
  }

  /**
   * Assess mental health needs and risk factors
   */
  private assessMentalHealthNeeds(request: AgentRequest): MentalHealthAssessment {
    const message = request.userMessage.toLowerCase();
    let riskLevel: 'low' | 'moderate' | 'high' = 'low';
    let primaryConcern = 'general_mental_health';
    const symptoms: string[] = [];
    const riskFactors: string[] = [];

    // Depression indicators
    const depressionKeywords = [
      'depressed', 'sad', 'hopeless', 'worthless', 'empty',
      'no energy', 'can\'t sleep', 'sleeping too much', 'no appetite'
    ];

    // Anxiety indicators
    const anxietyKeywords = [
      'anxious', 'worried', 'panic', 'nervous', 'scared',
      'racing heart', 'can\'t breathe', 'sweating', 'trembling'
    ];

    // Trauma indicators
    const traumaKeywords = [
      'trauma', 'ptsd', 'flashbacks', 'nightmares',
      'triggered', 'abuse', 'assault', 'accident'
    ];

    // Assess depression
    depressionKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        symptoms.push(keyword);
        if (primaryConcern === 'general_mental_health') {
          primaryConcern = 'depression';
        }
      }
    });

    // Assess anxiety
    anxietyKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        symptoms.push(keyword);
        if (primaryConcern === 'general_mental_health') {
          primaryConcern = 'anxiety';
        }
      }
    });

    // Assess trauma
    traumaKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        symptoms.push(keyword);
        primaryConcern = 'trauma'; // Trauma takes priority
        riskLevel = 'high';
      }
    });

    // Determine risk level based on symptoms
    if (symptoms.length >= 4) riskLevel = 'high';
    else if (symptoms.length >= 2) riskLevel = 'moderate';

    return {
      riskLevel,
      primaryConcern,
      symptoms,
      riskFactors,
      recommendedInterventions: this.getRecommendedInterventions(primaryConcern, riskLevel)
    };
  }

  /**
   * Generate specialized mental health response
   */
  private async generateMentalHealthResponse(request: AgentRequest, assessment: MentalHealthAssessment): Promise<string> {
    const hasDepression = assessment.primaryConcern === 'depression';
    const hasAnxiety = assessment.primaryConcern === 'anxiety';

    if (hasDepression) {
      return `Thank you for reaching out about your mental health. It takes courage to seek support, and I want you to know that what you're experiencing is valid and treatable.

**UNDERSTANDING DEPRESSION:**

Depression is a common but serious mental health condition that affects how you feel, think, and handle daily activities. You're not alone - depression affects millions of people, and effective treatments are available.

**COMMON DEPRESSION SYMPTOMS:**
- Persistent sad, anxious, or empty mood
- Loss of interest in activities once enjoyed
- Fatigue and decreased energy
- Difficulty concentrating or making decisions
- Changes in sleep or appetite
- Feelings of worthlessness or guilt

**EVIDENCE-BASED COPING STRATEGIES:**
1. **Behavioral Activation**: Engage in small, meaningful activities daily
2. **Sleep Hygiene**: Maintain regular sleep schedule (7-9 hours)
3. **Physical Activity**: Even 10-15 minutes of movement can help
4. **Social Connection**: Reach out to supportive friends or family
5. **Mindfulness**: Practice grounding techniques and present-moment awareness

**RECOMMENDED INTERVENTIONS:**
${assessment.recommendedInterventions.join('\n')}

**PROFESSIONAL SUPPORT OPTIONS:**
- **Therapy**: Cognitive Behavioral Therapy (CBT) is highly effective for depression
- **Medication**: Antidepressants can be helpful when combined with therapy
- **Support Groups**: Connecting with others who understand your experience
- **Crisis Support**: 988 Suicide & Crisis Lifeline available 24/7

**IMPORTANT**: Depression is treatable, and you can feel better. Professional mental health treatment is highly effective, and many people experience significant improvement with appropriate care.

What specific aspects of your mental health would you like to explore or address?`;
    }

    if (hasAnxiety) {
      return `I understand you're dealing with anxiety, and I want to help you find effective ways to manage these feelings. Anxiety is very treatable, and there are many strategies that can provide relief.

**UNDERSTANDING ANXIETY:**

Anxiety is your body's natural response to stress, but when it becomes overwhelming or persistent, it can interfere with daily life. The good news is that anxiety disorders are among the most treatable mental health conditions.

**IMMEDIATE ANXIETY RELIEF TECHNIQUES:**
1. **Deep Breathing**: 4-7-8 breathing (inhale 4, hold 7, exhale 8)
2. **Grounding**: 5-4-3-2-1 technique (5 things you see, 4 hear, 3 touch, 2 smell, 1 taste)
3. **Progressive Muscle Relaxation**: Tense and release muscle groups
4. **Mindfulness**: Focus on present moment without judgment

**LONGER-TERM ANXIETY MANAGEMENT:**
- **Regular Exercise**: Physical activity reduces anxiety and stress hormones
- **Limit Caffeine**: Can worsen anxiety symptoms
- **Adequate Sleep**: Poor sleep increases anxiety vulnerability
- **Stress Management**: Identify and address sources of stress
- **Social Support**: Connect with understanding friends and family

**RECOMMENDED INTERVENTIONS:**
${assessment.recommendedInterventions.join('\n')}

**WHEN TO SEEK IMMEDIATE HELP:**
- Panic attacks that feel uncontrollable
- Anxiety preventing you from daily activities
- Physical symptoms like chest pain or difficulty breathing
- Thoughts of self-harm

**PROFESSIONAL TREATMENT OPTIONS:**
- **Cognitive Behavioral Therapy (CBT)**: Highly effective for anxiety
- **Exposure Therapy**: Gradual exposure to anxiety triggers
- **Medication**: Anti-anxiety medications when appropriate
- **Mindfulness-Based Therapies**: MBSR, ACT

Remember: Anxiety is treatable, and you don't have to suffer alone. Professional help can provide you with effective tools and strategies for managing anxiety.

What specific anxiety symptoms or situations would you like help addressing?`;
    }

    return `Hello, I'm Dr. Jennifer Park, a psychiatrist specializing in mental health support and therapeutic care. I'm here to provide a safe, non-judgmental space to discuss your mental health concerns.

**MENTAL HEALTH ASSESSMENT:**
- Risk Level: ${assessment.riskLevel.toUpperCase()}
- Primary Concern: ${assessment.primaryConcern.replace('_', ' ').toUpperCase()}
- Symptoms Identified: ${assessment.symptoms.join(', ') || 'General mental health support'}

**MY MENTAL HEALTH EXPERTISE:**
- Depression and mood disorders
- Anxiety and panic disorders
- Trauma and PTSD support
- Crisis intervention and safety planning
- Stress management and coping skills
- Substance use and mental health
- Sleep and mental wellness

**FUNDAMENTAL MENTAL HEALTH PRINCIPLES:**
- Mental health is just as important as physical health
- Seeking help is a sign of strength, not weakness
- Recovery and healing are possible with appropriate support
- You are not defined by your mental health challenges
- Professional treatment is effective and evidence-based

**RECOMMENDED INTERVENTIONS:**
${assessment.recommendedInterventions.join('\n')}

**IMMEDIATE SUPPORT RESOURCES:**
- **Crisis Support**: 988 Suicide & Crisis Lifeline (call or text)
- **Crisis Text Line**: Text HOME to 741741
- **SAMHSA Helpline**: 1-************ (treatment referrals)

**REMEMBER:**
You deserve support, care, and healing. Mental health challenges are common, treatable, and nothing to be ashamed of. Taking the step to reach out shows incredible courage and self-awareness.

What brings you here today? How can I best support your mental health and wellbeing?`;
  }

  /**
   * Get recommended interventions based on assessment
   */
  private getRecommendedInterventions(concern: string, riskLevel: string): string[] {
    const interventions: Record<string, string[]> = {
      depression: [
        '• Schedule appointment with mental health professional within 1-2 weeks',
        '• Consider therapy (CBT, IPT, or behavioral activation)',
        '• Evaluate need for psychiatric medication consultation',
        '• Implement daily structure and behavioral activation',
        '• Build social support network and reduce isolation'
      ],
      anxiety: [
        '• Learn and practice anxiety management techniques daily',
        '• Consider cognitive behavioral therapy (CBT) for anxiety',
        '• Evaluate lifestyle factors (caffeine, sleep, exercise)',
        '• Practice mindfulness and relaxation techniques',
        '• Gradual exposure to anxiety triggers with professional guidance'
      ],
      trauma: [
        '• Seek trauma-informed therapy (EMDR, CPT, or PE)',
        '• Prioritize safety and stabilization',
        '• Build coping skills and emotional regulation',
        '• Consider support groups for trauma survivors',
        '• Address any co-occurring mental health conditions'
      ],
      general_mental_health: [
        '• Schedule mental health screening with professional',
        '• Focus on stress management and self-care',
        '• Build healthy coping strategies and resilience',
        '• Maintain social connections and support systems',
        '• Consider preventive mental health care'
      ]
    };

    return interventions[concern] || interventions.general_mental_health || [];
  }

  /**
   * Analyze need for specialist collaboration
   */
  private analyzeForSpecialistCollaboration(request: AgentRequest): AgentHandoffSuggestion[] {
    const message = request.userMessage.toLowerCase();
    const suggestions: AgentHandoffSuggestion[] = [];

    // Medical evaluation for physical symptoms
    if (message.includes('chest pain') || message.includes('heart racing') || message.includes('physical symptoms')) {
      suggestions.push({
        targetAgentRole: 'general_practitioner',
        reason: 'Physical symptoms may require medical evaluation to rule out medical causes',
        urgency: 'medium',
        contextToTransfer: 'Patient with anxiety/panic symptoms - medical clearance recommended'
      });
    }

    // Nutrition support for eating disorders
    if (message.includes('eating') || message.includes('food') || message.includes('weight')) {
      suggestions.push({
        targetAgentRole: 'nutritionist',
        reason: 'Eating concerns may benefit from nutritional counseling alongside mental health support',
        urgency: 'medium',
        contextToTransfer: 'Patient with eating/nutrition concerns - integrated mental health and nutrition care'
      });
    }

    // Emergency for crisis situations
    if (message.includes('emergency') || message.includes('crisis') || message.includes('urgent')) {
      suggestions.push({
        targetAgentRole: 'emergency',
        reason: 'Mental health crisis may require emergency intervention support',
        urgency: 'high',
        contextToTransfer: 'Mental health crisis situation - emergency support coordination needed'
      });
    }

    return suggestions;
  }

  /**
   * Calculate confidence for mental health consultations
   */
  private calculateMentalHealthConfidence(request: AgentRequest): number {
    let confidence = 0.9; // High base confidence for mental health

    const message = request.userMessage.toLowerCase();

    // High confidence for mental health topics
    const mentalHealthKeywords = [
      'mental health', 'depression', 'anxiety', 'stress', 'mood',
      'therapy', 'counseling', 'psychiatric', 'emotional', 'psychological'
    ];

    if (mentalHealthKeywords.some(keyword => message.includes(keyword))) {
      confidence = 0.95;
    }

    // Lower confidence for primarily medical topics
    const medicalKeywords = [
      'surgery', 'medication', 'physical pain', 'infection', 'injury'
    ];

    if (medicalKeywords.some(keyword => message.includes(keyword))) {
      confidence -= 0.2;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Generate mental health follow-up actions
   */
  private generateMentalHealthFollowUp(request: AgentRequest, assessment: MentalHealthAssessment) {
    const actions = [];

    // Always recommend mental health follow-up
    actions.push({
      type: 'schedule_appointment' as const,
      description: 'Schedule appointment with mental health professional for comprehensive evaluation',
      timeframe: this.getFollowUpTimeframe(assessment.riskLevel),
      priority: assessment.riskLevel === 'high' ? 'high' as const : 'medium' as const
    });

    // High risk cases need crisis planning
    if (assessment.riskLevel === 'high') {
      actions.push({
        type: 'specialist_referral' as const,
        description: 'Develop safety plan and crisis intervention resources',
        timeframe: 'Within 24-48 hours',
        priority: 'high' as const
      });
    }

    return actions;
  }

  /**
   * Get follow-up timeframe based on risk level
   */
  private getFollowUpTimeframe(riskLevel: string): string {
    switch (riskLevel) {
      case 'high': return 'Within 24-48 hours (urgent)';
      case 'moderate': return 'Within 1 week';
      default: return 'Within 2-4 weeks';
    }
  }

  /**
   * Enhanced confidence scoring for mental health requests
   */
  getConfidenceScore(request: AgentRequest): number {
    let score = super.getConfidenceScore(request);

    // Mental health agent is highly confident for psychological issues
    const mentalHealthKeywords = [
      'mental', 'depression', 'anxiety', 'stress', 'mood', 'emotional',
      'therapy', 'counseling', 'psychiatric', 'psychological', 'crisis'
    ];

    const message = request.userMessage.toLowerCase();
    if (mentalHealthKeywords.some(keyword => message.includes(keyword))) {
      score += 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }
}

interface MentalHealthAssessment {
  riskLevel: 'low' | 'moderate' | 'high';
  primaryConcern: string;
  symptoms: string[];
  riskFactors: string[];
  recommendedInterventions: string[];
}

export default MentalHealthAgent;
