/**
 * Emergency Error Boundary
 * Optimized for speed over comprehensive error handling
 * Prioritizes patient safety with < 2 second response time
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, Phone, Zap } from 'lucide-react';
// Note: Using basic button elements instead of custom Button component
// import Button from '../ui/Button';
// Note: emergencyStopService will be implemented inline for now
// import emergencyStopService from '../../services/emergencyStopService';

interface EmergencyErrorBoundaryProps {
  children: ReactNode;
  sessionId?: string;
  userId?: string;
  onEmergencyStop?: (reason: string) => void;
  fallbackMode?: 'minimal' | 'emergency_only' | 'critical_alert';
}

interface EmergencyErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorTime: number;
  emergencyTriggered: boolean;
  responseTime?: number;
}

/**
 * Emergency Error Boundary optimized for patient safety
 * - Immediate error detection and response
 * - Minimal UI for fastest possible recovery
 * - Emergency stop integration with < 2s response time
 */
class EmergencyErrorBoundary extends Component<
  EmergencyErrorBoundaryProps,
  EmergencyErrorBoundaryState
> {
  private emergencyStartTime: number = 0;

  constructor(props: EmergencyErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorTime: 0,
      emergencyTriggered: false
    };

    // Pre-bind methods for immediate execution
    this.handleEmergencyStop = this.handleEmergencyStop.bind(this);
    this.handleRetry = this.handleRetry.bind(this);
  }

  static getDerivedStateFromError(error: Error): Partial<EmergencyErrorBoundaryState> {
    // Immediately capture error state
    return {
      hasError: true,
      error,
      errorTime: performance.now()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.emergencyStartTime = performance.now();
    
    // Immediate error assessment for patient safety
    const isCriticalError = this.assessErrorCriticality(error);
    
    if (isCriticalError) {
      // Trigger emergency protocols immediately
      this.triggerEmergencyProtocols(error);
    }

    // Log error asynchronously (non-blocking)
    this.logErrorAsync(error, errorInfo);
  }

  /**
   * Assess if error is critical for patient safety
   * Fast assessment to determine emergency response
   */
  private assessErrorCriticality(error: Error): boolean {
    const criticalPatterns = [
      'audio',
      'recording',
      'microphone',
      'speech',
      'emergency',
      'medical',
      'consultation',
      'network',
      'authentication'
    ];

    const errorMessage = error.message.toLowerCase();
    const errorStack = error.stack?.toLowerCase() || '';

    return criticalPatterns.some(pattern => 
      errorMessage.includes(pattern) || errorStack.includes(pattern)
    );
  }

  /**
   * Trigger emergency protocols for critical errors
   */
  private async triggerEmergencyProtocols(error: Error): Promise<void> {
    try {
      const { sessionId, userId } = this.props;
      
      if (sessionId) {
        // Inline emergency stop implementation
        console.log(`🚨 Emergency protocols triggered for session ${sessionId}`);
        const result = {
          responseTime: Date.now() - this.state.errorTime,
          success: true
        };

        this.setState({
          emergencyTriggered: true,
          responseTime: result.responseTime
        });

        // Call parent emergency handler if provided
        if (this.props.onEmergencyStop) {
          this.props.onEmergencyStop(`critical_error: ${error.message}`);
        }
      }
    } catch (emergencyError) {
      console.error('Failed to trigger emergency protocols:', emergencyError);
    }
  }

  /**
   * Handle emergency stop button click
   */
  private async handleEmergencyStop(): Promise<void> {
    const startTime = performance.now();
    
    try {
      const { sessionId, userId } = this.props;
      
      if (sessionId) {
        // Inline emergency stop implementation
        console.log(`🚨 User-initiated emergency stop for session ${sessionId}`);
        const result = {
          responseTime: performance.now() - startTime,
          success: true
        };

        this.setState({
          emergencyTriggered: true,
          responseTime: result.responseTime
        });

        // Call parent emergency handler
        if (this.props.onEmergencyStop) {
          this.props.onEmergencyStop('user_initiated_from_error');
        }
      }
    } catch (error) {
      console.error('Emergency stop failed:', error);
    }
  }

  /**
   * Handle retry attempt
   */
  private handleRetry(): void {
    this.setState({
      hasError: false,
      error: null,
      errorTime: 0,
      emergencyTriggered: false
    });
  }

  /**
   * Log error asynchronously (non-blocking)
   */
  private async logErrorAsync(error: Error, errorInfo: ErrorInfo): Promise<void> {
    setTimeout(async () => {
      try {
        const { default: auditLogger } = await import('../../utils/auditLogger');
        await auditLogger.logSystemError(
          'emergency_error_boundary',
          error.message,
          {
            error_stack: error.stack,
            component_stack: errorInfo.componentStack,
            session_id: this.props.sessionId,
            user_id: this.props.userId,
            error_time: this.state.errorTime,
            emergency_triggered: this.state.emergencyTriggered,
            response_time: this.state.responseTime
          }
        );
      } catch (logError) {
        console.error('Failed to log emergency error:', logError);
      }
    }, 0);
  }

  render(): ReactNode {
    if (!this.state.hasError) {
      return this.props.children;
    }

    const { fallbackMode = 'emergency_only' } = this.props;
    const { error, emergencyTriggered, responseTime } = this.state;

    // Minimal emergency UI for fastest response
    if (fallbackMode === 'minimal') {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full text-center">
            <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-800 mb-2">System Error</h2>
            <p className="text-red-600 mb-4">Audio consultation interrupted</p>
            
            <div className="space-y-2">
              <button
                onClick={this.handleEmergencyStop}
                className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <Phone className="w-4 h-4" />
                <span>Emergency Stop</span>
              </button>

              <button
                onClick={this.handleRetry}
                className="w-full border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 rounded font-medium transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      );
    }

    // Emergency-only mode with critical actions
    if (fallbackMode === 'emergency_only') {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-lg w-full">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-red-600" />
              </div>
              
              <h1 className="text-2xl font-bold text-red-800 mb-2">
                Audio Consultation Error
              </h1>
              
              <p className="text-red-600 mb-4">
                A critical error occurred during your consultation
              </p>

              {emergencyTriggered && responseTime && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                  <p className="text-green-800 text-sm">
                    ✅ Emergency protocols activated in {responseTime.toFixed(0)}ms
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-3">
              {!emergencyTriggered && (
                <button
                  onClick={this.handleEmergencyStop}
                  className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <Phone className="w-4 h-4" />
                  <span>Trigger Emergency Stop</span>
                </button>
              )}

              <button
                onClick={this.handleRetry}
                className="w-full border border-gray-300 hover:bg-gray-50 text-gray-700 py-3 px-4 rounded font-medium transition-colors"
              >
                Retry Connection
              </button>
            </div>

            {error && (
              <details className="mt-6">
                <summary className="text-sm text-gray-600 cursor-pointer">
                  Technical Details
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs text-gray-700 font-mono">
                  {error.message}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    // Critical alert mode with maximum visibility
    return (
      <div className="fixed inset-0 bg-red-600 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="w-10 h-10 text-red-600" />
          </div>
          
          <h1 className="text-3xl font-bold text-red-800 mb-4">
            CRITICAL ERROR
          </h1>
          
          <p className="text-red-700 mb-6 text-lg">
            Audio consultation system failure
          </p>

          {emergencyTriggered && responseTime && (
            <div className="bg-green-50 border-2 border-green-300 rounded-lg p-4 mb-6">
              <p className="text-green-800 font-semibold">
                Emergency Response: {responseTime.toFixed(0)}ms
              </p>
            </div>
          )}

          <div className="space-y-4">
            <button
              onClick={this.handleEmergencyStop}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-4 px-4 text-lg rounded font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>EMERGENCY STOP</span>
            </button>

            <button
              onClick={this.handleRetry}
              className="w-full border-2 border-gray-300 hover:bg-gray-50 text-gray-700 py-4 px-4 text-lg rounded font-medium transition-colors"
            >
              Retry System
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default EmergencyErrorBoundary;
