/**
 * EDUCATIONAL CONTENT API ENDPOINT
 * 
 * Provides secure API endpoints for personalized patient education
 * content generation and delivery with cultural adaptation.
 * 
 * FEATURES:
 * - Culturally sensitive educational content generation
 * - Multi-language support with regional adaptations
 * - Health literacy level adaptation
 * - Post-consultation educational follow-up
 * - HIPAA-compliant content management
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, param, query, validationResult } = require('express-validator');
const authMiddleware = require('./middleware/authMiddleware');
const rbacMiddleware = require('./middleware/rbacMiddleware');
const auditLogger = require('./utils/auditLogger');
const { createClient } = require('@supabase/supabase-js');

const router = express.Router();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Rate limiting configuration
const educationRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // 50 requests per minute
  message: { error: 'Rate limit exceeded for educational content' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.user?.id || req.ip
});

// Validation middleware
const validateContentGeneration = [
  body('sessionId').isUUID().withMessage('Valid session ID required'),
  body('consultationSummary').isString().isLength({ min: 10, max: 2000 }).withMessage('Consultation summary required (10-2000 chars)'),
  body('diagnosedConditions').optional().isArray().withMessage('Diagnosed conditions must be an array'),
  body('recommendedTreatments').optional().isArray().withMessage('Recommended treatments must be an array'),
  body('culturalContext').optional().isObject().withMessage('Cultural context must be an object'),
  body('patientProfile').optional().isObject().withMessage('Patient profile must be an object'),
  body('urgencyLevel').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Valid urgency level required'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }
    next();
  }
];

/**
 * POST /api/educational-content/generate
 * Generate personalized educational content
 */
router.post('/generate',
  educationRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'read'),
  validateContentGeneration,
  async (req, res) => {
    const startTime = Date.now();
    const {
      sessionId,
      consultationSummary,
      diagnosedConditions = [],
      recommendedTreatments = [],
      culturalContext = {},
      patientProfile = {},
      urgencyLevel = 'medium'
    } = req.body;

    try {
      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Prepare education request
      const educationRequest = {
        sessionId,
        userId: req.user.id,
        consultationSummary,
        diagnosedConditions,
        recommendedTreatments,
        culturalContext: {
          countryCode: culturalContext.countryCode || 'US',
          language: culturalContext.language || 'English',
          culturalConsiderations: culturalContext.culturalConsiderations || [],
          familyInvolvementLevel: culturalContext.familyInvolvementLevel || 'medium',
          traditionalMedicineAwareness: culturalContext.traditionalMedicineAwareness || true
        },
        patientProfile: {
          age: patientProfile.age || req.user.age || 30,
          gender: patientProfile.gender || req.user.gender || 'unknown',
          healthLiteracyLevel: patientProfile.healthLiteracyLevel || 'intermediate',
          preferredLanguage: patientProfile.preferredLanguage || 'English'
        },
        urgencyLevel
      };

      // Generate educational content
      const educationalContent = await generateEducationalContent(educationRequest);

      // Store educational content in database
      const contentIds = await storeEducationalContent(sessionId, req.user.id, educationalContent);

      // Audit log the content generation
      await auditLogger.logDataAccess('educational_content_generation', sessionId, true, {
        operation: 'generate_educational_content',
        session_id: sessionId,
        user_id: req.user.id,
        content_items: educationalContent.length,
        diagnosed_conditions: diagnosedConditions.length,
        cultural_context: culturalContext.countryCode,
        health_literacy_level: patientProfile.healthLiteracyLevel,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          contentItems: educationalContent.length,
          educationalContent: educationalContent.map((content, index) => ({
            id: contentIds[index],
            title: content.title,
            contentType: content.contentType,
            targetAudience: content.targetAudience,
            estimatedReadingTime: content.estimatedReadingTime,
            keyTakeaways: content.keyTakeaways,
            actionItems: content.actionItems
          })),
          culturalAdaptations: educationRequest.culturalContext.culturalConsiderations.length,
          healthLiteracyLevel: educationRequest.patientProfile.healthLiteracyLevel
        },
        metadata: {
          processingTime: Date.now() - startTime,
          sessionId,
          urgencyLevel
        }
      });

    } catch (error) {
      console.error('❌ Educational content generation error:', error);

      await auditLogger.logDataAccess('educational_content_generation', sessionId, false, {
        operation: 'generate_educational_content',
        session_id: sessionId,
        user_id: req.user.id,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to generate educational content'
      });
    }
  }
);

/**
 * GET /api/educational-content/session/:sessionId
 * Get educational content for a session
 */
router.get('/session/:sessionId',
  educationRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'read'),
  [param('sessionId').isUUID().withMessage('Valid session ID required')],
  async (req, res) => {
    try {
      const { sessionId } = req.params;

      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Get educational content for the session
      const { data: educationalContent, error } = await supabase
        .from('educational_content')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: educationalContent || []
      });

    } catch (error) {
      console.error('❌ Get educational content error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve educational content'
      });
    }
  }
);

/**
 * GET /api/educational-content/:contentId
 * Get specific educational content item
 */
router.get('/:contentId',
  educationRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'read'),
  [param('contentId').isUUID().withMessage('Valid content ID required')],
  async (req, res) => {
    try {
      const { contentId } = req.params;

      // Get educational content with session verification
      const { data: content, error } = await supabase
        .from('educational_content')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', contentId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (error || !content) {
        return res.status(403).json({
          success: false,
          error: 'Educational content not found or access denied'
        });
      }

      // Track content access
      await auditLogger.logDataAccess('educational_content_access', content.session_id, true, {
        operation: 'access_educational_content',
        content_id: contentId,
        session_id: content.session_id,
        user_id: req.user.id,
        content_type: content.content_type
      });

      res.json({
        success: true,
        data: content
      });

    } catch (error) {
      console.error('❌ Get educational content item error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve educational content item'
      });
    }
  }
);

/**
 * POST /api/educational-content/feedback
 * Submit feedback on educational content
 */
router.post('/feedback',
  educationRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'create'),
  [
    body('contentId').isUUID().withMessage('Valid content ID required'),
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be 1-5'),
    body('feedback').optional().isString().isLength({ max: 1000 }).withMessage('Feedback must be max 1000 chars'),
    body('helpful').isBoolean().withMessage('Helpful flag required')
  ],
  async (req, res) => {
    const startTime = Date.now();
    const { contentId, rating, feedback, helpful } = req.body;

    try {
      // Verify content ownership
      const { data: content, error: contentError } = await supabase
        .from('educational_content')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', contentId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (contentError || !content) {
        return res.status(403).json({
          success: false,
          error: 'Educational content not found or access denied'
        });
      }

      // Store feedback
      const { data: feedbackRecord, error } = await supabase
        .from('educational_content_feedback')
        .insert({
          content_id: contentId,
          user_id: req.user.id,
          session_id: content.session_id,
          rating,
          feedback,
          helpful
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Audit log the feedback
      await auditLogger.logDataAccess('educational_content_feedback', content.session_id, true, {
        operation: 'submit_content_feedback',
        content_id: contentId,
        session_id: content.session_id,
        user_id: req.user.id,
        rating,
        helpful,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          feedbackId: feedbackRecord.id,
          contentId,
          rating,
          helpful,
          submittedAt: feedbackRecord.created_at
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Educational content feedback error:', error);

      await auditLogger.logDataAccess('educational_content_feedback', 'unknown', false, {
        operation: 'submit_content_feedback',
        content_id: contentId,
        user_id: req.user.id,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to submit feedback'
      });
    }
  }
);

/**
 * Generate educational content (mock implementation)
 */
async function generateEducationalContent(request) {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const educationalContent = [];

  // Generate condition explanations
  for (const condition of request.diagnosedConditions) {
    educationalContent.push({
      title: `Understanding ${condition}`,
      content: `Educational content about ${condition} adapted for ${request.culturalContext.countryCode}`,
      contentType: 'explanation',
      targetAudience: 'patient',
      culturalAdaptations: request.culturalContext.culturalConsiderations,
      languageCode: request.patientProfile.preferredLanguage,
      healthLiteracyLevel: request.patientProfile.healthLiteracyLevel,
      estimatedReadingTime: Math.ceil(Math.random() * 5 + 2),
      keyTakeaways: [
        `${condition} is a manageable condition`,
        'Early treatment improves outcomes',
        'Regular follow-up is important'
      ],
      actionItems: [
        'Schedule follow-up appointment',
        'Monitor symptoms daily',
        'Take medications as prescribed'
      ],
      resources: generateLocalResources(request.culturalContext.countryCode)
    });
  }

  // Generate treatment instructions
  for (const treatment of request.recommendedTreatments) {
    educationalContent.push({
      title: `${treatment} Instructions`,
      content: `Detailed instructions for ${treatment}`,
      contentType: 'instructions',
      targetAudience: 'patient',
      culturalAdaptations: request.culturalContext.culturalConsiderations,
      languageCode: request.patientProfile.preferredLanguage,
      healthLiteracyLevel: request.patientProfile.healthLiteracyLevel,
      estimatedReadingTime: Math.ceil(Math.random() * 3 + 1),
      keyTakeaways: [
        'Follow instructions carefully',
        'Complete full treatment course',
        'Report side effects immediately'
      ],
      actionItems: [
        'Set medication reminders',
        'Track treatment progress',
        'Contact provider with questions'
      ],
      resources: generateLocalResources(request.culturalContext.countryCode)
    });
  }

  // Add general health education if no specific conditions
  if (educationalContent.length === 0) {
    educationalContent.push({
      title: 'General Health Guidance',
      content: 'General health education content',
      contentType: 'prevention',
      targetAudience: 'patient',
      culturalAdaptations: request.culturalContext.culturalConsiderations,
      languageCode: request.patientProfile.preferredLanguage,
      healthLiteracyLevel: request.patientProfile.healthLiteracyLevel,
      estimatedReadingTime: 3,
      keyTakeaways: [
        'Maintain regular health checkups',
        'Follow healthy lifestyle practices',
        'Stay informed about your health'
      ],
      actionItems: [
        'Schedule annual checkup',
        'Maintain healthy diet',
        'Exercise regularly'
      ],
      resources: generateLocalResources(request.culturalContext.countryCode)
    });
  }

  return educationalContent;
}

/**
 * Store educational content in database
 */
async function storeEducationalContent(sessionId, userId, educationalContent) {
  const contentIds = [];

  for (const content of educationalContent) {
    const { data, error } = await supabase
      .from('educational_content')
      .insert({
        session_id: sessionId,
        user_id: userId,
        title: content.title,
        content: content.content,
        content_type: content.contentType,
        target_audience: content.targetAudience,
        cultural_adaptations: content.culturalAdaptations,
        language_code: content.languageCode,
        health_literacy_level: content.healthLiteracyLevel,
        estimated_reading_time: content.estimatedReadingTime,
        key_takeaways: content.keyTakeaways,
        action_items: content.actionItems,
        resources: content.resources
      })
      .select('id')
      .single();

    if (!error && data) {
      contentIds.push(data.id);
    }
  }

  return contentIds;
}

/**
 * Generate local healthcare resources
 */
function generateLocalResources(countryCode) {
  const resources = [];

  switch (countryCode) {
    case 'GH': // Ghana
      resources.push(
        { type: 'phone', title: 'Emergency Services', value: '999', description: 'For medical emergencies' },
        { type: 'phone', title: 'National Health Insurance', value: '0302-661-360', description: 'For health insurance inquiries' }
      );
      break;
    case 'NG': // Nigeria
      resources.push(
        { type: 'phone', title: 'Emergency Services', value: '199', description: 'For medical emergencies' }
      );
      break;
    case 'KE': // Kenya
      resources.push(
        { type: 'phone', title: 'Emergency Services', value: '999', description: 'For medical emergencies' }
      );
      break;
    default:
      resources.push(
        { type: 'phone', title: 'Emergency Services', value: 'Local emergency number', description: 'Contact your local emergency services' }
      );
  }

  return resources;
}

module.exports = router;
