/**
 * EDUCATION AGENT
 * 
 * This specialized agent generates culturally relevant, personalized educational
 * content for patients based on their consultation outcomes, health conditions,
 * and cultural context. It operates as a background service to enhance patient
 * understanding and health literacy.
 * 
 * FEATURES:
 * - Culturally sensitive health education content
 * - Multi-language support with regional adaptations
 * - Condition-specific educational materials
 * - Health literacy level adaptation
 * - Family involvement considerations
 * - Traditional medicine integration awareness
 * - Post-consultation educational follow-up
 */

import { BaseAgent } from './BaseAgent';
import { RAGTool } from '../tools/RAGTool';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { supabase } from '../utils/supabaseClient';
import auditLogger from '../utils/auditLogger';
import type { 
  IAgent, 
  AgentRequest, 
  AgentResponse, 
  AgentCapability,
  AgentRole
} from './BaseAgent';

interface EducationalContent {
  id: string;
  title: string;
  content: string;
  contentType: 'explanation' | 'instructions' | 'prevention' | 'lifestyle' | 'medication' | 'warning_signs';
  targetAudience: 'patient' | 'family' | 'caregiver';
  culturalAdaptations: string[];
  languageCode: string;
  healthLiteracyLevel: 'basic' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  keyTakeaways: string[];
  actionItems: string[];
  resources: Array<{
    type: 'link' | 'phone' | 'location';
    title: string;
    value: string;
    description?: string;
  }>;
}

interface EducationRequest {
  sessionId: string;
  userId: string;
  consultationSummary: string;
  diagnosedConditions: string[];
  recommendedTreatments: string[];
  culturalContext: {
    countryCode: string;
    language: string;
    culturalConsiderations: string[];
    familyInvolvementLevel: 'low' | 'medium' | 'high';
    traditionalMedicineAwareness: boolean;
  };
  patientProfile: {
    age: number;
    gender: string;
    educationLevel?: string;
    healthLiteracyLevel: 'basic' | 'intermediate' | 'advanced';
    preferredLanguage: string;
  };
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
}

export class EducationAgent extends BaseAgent {
  private ragTool: RAGTool;
  private contentCache: Map<string, EducationalContent[]> = new Map();

  constructor(memoryManager: any) {
    const id = 'education-agent-001';
    const name = 'Dr. Amara Osei';
    const role: AgentRole = 'education_specialist' as AgentRole;
    const capabilities: AgentCapability[] = [
      'patient_education',
      'cultural_adaptation',
      'health_literacy_assessment',
      'content_personalization'
    ] as AgentCapability[];

    const systemPrompt = `You are Dr. Amara Osei, a Health Education Specialist with expertise in culturally sensitive patient education and health literacy. You create personalized educational content that empowers patients to understand and manage their health conditions effectively.

CORE EXPERTISE:
- Cross-cultural health communication
- Health literacy assessment and adaptation
- Patient education content development
- Family-centered care approaches
- Traditional medicine integration awareness
- Multi-language health communication

EDUCATIONAL CONTENT CREATION:
- Adapt content to patient's health literacy level
- Incorporate cultural beliefs and practices
- Consider family involvement expectations
- Provide actionable, practical guidance
- Include warning signs and when to seek help
- Offer culturally appropriate lifestyle recommendations

CULTURAL SENSITIVITY GUIDELINES:
- Respect traditional healing practices while promoting evidence-based care
- Consider family decision-making structures
- Adapt communication styles to cultural norms
- Include culturally relevant examples and analogies
- Address potential barriers to care access
- Provide resources in appropriate languages

CONTENT TYPES:
1. Condition Explanations: Clear, simple explanations of health conditions
2. Treatment Instructions: Step-by-step guidance for treatments and medications
3. Prevention Strategies: Culturally appropriate prevention recommendations
4. Lifestyle Modifications: Practical lifestyle changes with cultural considerations
5. Warning Signs: When to seek immediate medical attention
6. Resource Connections: Local healthcare resources and support services

Remember: Your goal is to empower patients with knowledge while respecting their cultural context and health literacy level. Make complex medical information accessible and actionable.`;

    // Initialize with RAG tool for educational content retrieval
    const tools = [new RAGTool()];

    super(id, name, role, capabilities, systemPrompt, memoryManager, tools);

    // Store RAG tool reference
    this.ragTool = tools[0] as RAGTool;

    console.log('📚 Education Agent initialized');
  }

  /**
   * Generate personalized educational content
   */
  async generateEducationalContent(request: EducationRequest): Promise<EducationalContent[]> {
    const startTime = Date.now();

    try {
      console.log(`📚 Generating educational content for session: ${request.sessionId}`);

      // Check cache first
      const cacheKey = this.generateCacheKey(request);
      const cachedContent = this.contentCache.get(cacheKey);
      if (cachedContent) {
        console.log('✅ Using cached educational content');
        return cachedContent;
      }

      // Retrieve relevant educational materials using RAG
      const educationalMaterials = await this.retrieveEducationalMaterials(request);

      // Generate personalized content for each condition/topic
      const educationalContent: EducationalContent[] = [];

      // Generate condition explanations
      for (const condition of request.diagnosedConditions) {
        const conditionContent = await this.generateConditionExplanation(condition, request);
        if (conditionContent) {
          educationalContent.push(conditionContent);
        }
      }

      // Generate treatment instructions
      for (const treatment of request.recommendedTreatments) {
        const treatmentContent = await this.generateTreatmentInstructions(treatment, request);
        if (treatmentContent) {
          educationalContent.push(treatmentContent);
        }
      }

      // Generate prevention and lifestyle content
      const preventionContent = await this.generatePreventionGuidance(request);
      if (preventionContent) {
        educationalContent.push(preventionContent);
      }

      // Generate warning signs content
      const warningSignsContent = await this.generateWarningSignsContent(request);
      if (warningSignsContent) {
        educationalContent.push(warningSignsContent);
      }

      // Cache the generated content
      this.contentCache.set(cacheKey, educationalContent);

      // Store educational content in database
      await this.storeEducationalContent(request.sessionId, educationalContent);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Generated ${educationalContent.length} educational items in ${processingTime}ms`);

      return educationalContent;

    } catch (error) {
      console.error('❌ Educational content generation failed:', error);
      throw error;
    }
  }

  /**
   * Handle educational content requests
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      console.log(`📚 Education Agent processing request for session: ${request.sessionId}`);

      // Extract educational requirements from the request
      const educationRequest = this.extractEducationRequest(request);

      // Generate educational content
      const educationalContent = await this.generateEducationalContent(educationRequest);

      // Format response with educational content
      const response = this.formatEducationalResponse(educationalContent, educationRequest);

      const processingTime = Date.now() - startTime;

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence: 0.9,
        suggestedHandoffs: [],
        emergencyFlags: [],
        metadata: {
          processingTime,
          contentItemsGenerated: educationalContent.length,
          culturalAdaptations: educationRequest.culturalContext.culturalConsiderations.length,
          healthLiteracyLevel: educationRequest.patientProfile.healthLiteracyLevel
        }
      };

    } catch (error) {
      console.error('❌ Education Agent failed:', error);

      return {
        agentId: this.id,
        agentName: this.name,
        content: 'I apologize, but I\'m having difficulty generating educational content at the moment. Please consult with your healthcare provider for additional information about your condition and treatment.',
        confidence: 0.3,
        suggestedHandoffs: [],
        emergencyFlags: [],
        metadata: {
          processingTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Generate condition explanation content
   */
  private async generateConditionExplanation(
    condition: string, 
    request: EducationRequest
  ): Promise<EducationalContent | null> {
    try {
      // Use RAG to get condition information
      const conditionInfo = await this.ragTool.execute({
        query: `${condition} patient education explanation`,
        parameters: {
          maxResults: 3,
          minRelevanceScore: 0.8,
          documentTypes: ['patient_education', 'guideline'],
          countryFilter: request.culturalContext.countryCode,
          urgencyLevel: request.urgencyLevel
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: request.sessionId,
        agentId: this.id
      });

      // Generate culturally adapted explanation using AI
      const prompt = this.buildConditionExplanationPrompt(condition, request, conditionInfo);
      
      const aiResponse = await aiOrchestrator.generateResponse({
        messages: [
          { role: 'system', content: this.systemPrompt },
          { role: 'user', content: prompt }
        ],
        sessionId: request.sessionId,
        agentType: 'education_specialist',
        maxTokens: 800,
        temperature: 0.7
      });

      if (!aiResponse.success) {
        throw new Error('Failed to generate condition explanation');
      }

      // Parse and structure the response
      return this.parseEducationalContent(
        aiResponse.data?.content || 'No response available',
        'explanation',
        condition,
        request
      );

    } catch (error) {
      console.error(`Failed to generate condition explanation for ${condition}:`, error);
      return null;
    }
  }

  /**
   * Build condition explanation prompt
   */
  private buildConditionExplanationPrompt(
    condition: string,
    request: EducationRequest,
    ragInfo: any
  ): string {
    let prompt = `Create a patient education explanation for: ${condition}\n\n`;
    
    prompt += `PATIENT CONTEXT:\n`;
    prompt += `- Age: ${request.patientProfile.age}\n`;
    prompt += `- Gender: ${request.patientProfile.gender}\n`;
    prompt += `- Health Literacy Level: ${request.patientProfile.healthLiteracyLevel}\n`;
    prompt += `- Language: ${request.patientProfile.preferredLanguage}\n`;
    prompt += `- Country: ${request.culturalContext.countryCode}\n`;
    prompt += `- Cultural Considerations: ${request.culturalContext.culturalConsiderations.join(', ')}\n`;
    prompt += `- Family Involvement: ${request.culturalContext.familyInvolvementLevel}\n\n`;

    if (ragInfo && ragInfo.success && ragInfo.data) {
      prompt += `MEDICAL REFERENCE INFORMATION:\n${ragInfo.data.content}\n\n`;
    }

    prompt += `Please create a clear, culturally sensitive explanation that:\n`;
    prompt += `1. Explains what ${condition} is in simple terms\n`;
    prompt += `2. Describes common symptoms and causes\n`;
    prompt += `3. Addresses cultural beliefs or concerns\n`;
    prompt += `4. Provides reassurance and hope where appropriate\n`;
    prompt += `5. Includes 3-5 key takeaways\n`;
    prompt += `6. Suggests 2-3 immediate action items\n\n`;

    prompt += `Adapt the language and examples to be appropriate for the patient's cultural context and health literacy level.`;

    return prompt;
  }

  /**
   * Parse AI response into structured educational content
   */
  private parseEducationalContent(
    aiResponse: string,
    contentType: EducationalContent['contentType'],
    topic: string,
    request: EducationRequest
  ): EducationalContent {
    // This would parse the AI response more sophisticatedly in production
    // For now, create a structured response
    
    return {
      id: `${request.sessionId}-${contentType}-${Date.now()}`,
      title: `Understanding ${topic}`,
      content: aiResponse,
      contentType,
      targetAudience: 'patient',
      culturalAdaptations: request.culturalContext.culturalConsiderations,
      languageCode: request.patientProfile.preferredLanguage,
      healthLiteracyLevel: request.patientProfile.healthLiteracyLevel,
      estimatedReadingTime: Math.ceil(aiResponse.length / 200), // Rough estimate
      keyTakeaways: this.extractKeyTakeaways(aiResponse),
      actionItems: this.extractActionItems(aiResponse),
      resources: this.generateLocalResources(request.culturalContext.countryCode)
    };
  }

  /**
   * Extract key takeaways from content
   */
  private extractKeyTakeaways(content: string): string[] {
    // Simple extraction - would be more sophisticated in production
    const takeaways = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.includes('key') || line.includes('important') || line.includes('remember')) {
        takeaways.push(line.trim());
      }
    }

    return takeaways.slice(0, 5); // Limit to 5 takeaways
  }

  /**
   * Extract action items from content
   */
  private extractActionItems(content: string): string[] {
    // Simple extraction - would be more sophisticated in production
    const actions = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.includes('should') || line.includes('need to') || line.includes('action')) {
        actions.push(line.trim());
      }
    }

    return actions.slice(0, 3); // Limit to 3 actions
  }

  /**
   * Generate local healthcare resources
   */
  private generateLocalResources(countryCode: string): EducationalContent['resources'] {
    const resources: Array<{
      type: 'link' | 'phone' | 'location';
      title: string;
      value: string;
      description?: string;
    }> = [];

    // Add country-specific emergency numbers and resources
    switch (countryCode) {
      case 'GH': // Ghana
        resources.push(
          {
            type: 'phone',
            title: 'Emergency Services',
            value: '999',
            description: 'For medical emergencies'
          },
          {
            type: 'phone',
            title: 'National Health Insurance',
            value: '0302-661-360',
            description: 'For health insurance inquiries'
          }
        );
        break;
      case 'NG': // Nigeria
        resources.push(
          {
            type: 'phone',
            title: 'Emergency Services',
            value: '199',
            description: 'For medical emergencies'
          }
        );
        break;
      case 'KE': // Kenya
        resources.push(
          {
            type: 'phone',
            title: 'Emergency Services',
            value: '999',
            description: 'For medical emergencies'
          }
        );
        break;
      default:
        resources.push(
          {
            type: 'phone',
            title: 'Emergency Services',
            value: 'Local emergency number',
            description: 'Contact your local emergency services'
          }
        );
    }

    return resources;
  }

  /**
   * Extract education request from agent request
   */
  private extractEducationRequest(request: AgentRequest): EducationRequest {
    // This would extract more sophisticated information in production
    return {
      sessionId: request.sessionId,
      userId: request.patientContext?.userId || 'unknown',
      consultationSummary: request.userMessage,
      diagnosedConditions: ['General Health Concern'], // Would be extracted from context
      recommendedTreatments: ['General Care'], // Would be extracted from context
      culturalContext: {
        countryCode: request.patientContext?.regionalContext?.countryCode || 'US',
        language: request.patientContext?.preferredLanguage || 'English',
        culturalConsiderations: request.patientContext?.regionalContext?.culturalConsiderations || [],
        familyInvolvementLevel: 'medium',
        traditionalMedicineAwareness: true
      },
      patientProfile: {
        age: request.patientContext?.age || 30,
        gender: request.patientContext?.gender || 'unknown',
        healthLiteracyLevel: 'intermediate',
        preferredLanguage: request.patientContext?.preferredLanguage || 'English'
      },
      urgencyLevel: request.urgencyLevel || 'medium'
    };
  }

  /**
   * Format educational response
   */
  private formatEducationalResponse(
    content: EducationalContent[],
    request: EducationRequest
  ): string {
    let response = `📚 Personalized Health Education\n\n`;
    
    response += `I've prepared ${content.length} educational resources tailored to your cultural context and health literacy level:\n\n`;

    content.forEach((item, index) => {
      response += `${index + 1}. ${item.title}\n`;
      response += `   Type: ${item.contentType}\n`;
      response += `   Reading Time: ~${item.estimatedReadingTime} minutes\n\n`;
    });

    response += `These materials have been adapted for your cultural context and will be available in your patient portal for future reference.`;

    return response;
  }

  /**
   * Generate cache key for educational content
   */
  private generateCacheKey(request: EducationRequest): string {
    const keyComponents = [
      request.culturalContext.countryCode,
      request.patientProfile.healthLiteracyLevel,
      request.diagnosedConditions.join('-'),
      request.recommendedTreatments.join('-')
    ];
    
    return keyComponents.join('_');
  }

  /**
   * Store educational content in database
   */
  private async storeEducationalContent(
    sessionId: string,
    content: EducationalContent[]
  ): Promise<void> {
    try {
      // This would store content in a dedicated educational_content table
      console.log(`📚 Storing ${content.length} educational content items for session ${sessionId}`);
      
      // For now, just log the storage
      await auditLogger.logDataAccess('educational_content_generation', sessionId, true, {
        operation: 'generate_educational_content',
        session_id: sessionId,
        content_items: content.length,
        agent_id: this.id
      });

    } catch (error) {
      console.error('Failed to store educational content:', error);
    }
  }

  // Placeholder methods for other content types
  private async generateTreatmentInstructions(treatment: string, request: EducationRequest): Promise<EducationalContent | null> {
    // Would implement treatment instruction generation
    return null;
  }

  private async generatePreventionGuidance(request: EducationRequest): Promise<EducationalContent | null> {
    // Would implement prevention guidance generation
    return null;
  }

  private async generateWarningSignsContent(request: EducationRequest): Promise<EducationalContent | null> {
    // Would implement warning signs content generation
    return null;
  }

  private async retrieveEducationalMaterials(request: EducationRequest): Promise<any> {
    // Would retrieve relevant educational materials using RAG
    return null;
  }
}

// Export singleton instance
export const educationAgent = new EducationAgent(null);
