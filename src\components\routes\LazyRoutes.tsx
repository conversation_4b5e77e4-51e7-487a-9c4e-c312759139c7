/**
 * LAZY-LOADED ROUTES FOR MEDICAL APPLICATION (SIMPLIFIED)
 *
 * Main orchestrator for lazy loading routes with focused, maintainable structure.
 * This file provides core routing functionality without duplicate declarations.
 *
 * PERFORMANCE TARGETS:
 * - Emergency routes load < 100ms
 * - Role-based routes load < 500ms
 * - Offline fallbacks available
 */

import * as React from 'react';
import type { UserRole } from '../../types/auth';

// Lazy loading utility with fallback
let createLazyComponent: any;
try {
  const lazyModule = require('../../utils/lazyLoading');
  createLazyComponent = lazyModule.createLazyComponent;
} catch (error) {
  console.warn('Lazy loading utilities not available:', error);
  createLazyComponent = (loader: () => Promise<any>, options?: any) => {
    return () => React.createElement('div', null, 'Component loading...');
  };
}

// =============================================================================
// CORE ROUTE EXPORTS (Re-exported from modular files)
// =============================================================================

// Emergency Routes (Critical - Highest Priority)
let preloadEmergencyRoutes: () => void;
let checkEmergencyRoutesAvailability: () => Promise<boolean>;
let EmergencyProtocols: React.ComponentType;
let EmergencyConsultation: React.ComponentType;
let CriticalVitals: React.ComponentType;
let OfflineEmergencyProtocols: React.ComponentType;

try {
  const emergencyModule = require('./EmergencyRoutes');
  ({ EmergencyProtocols, EmergencyConsultation, CriticalVitals, OfflineEmergencyProtocols, preloadEmergencyRoutes, checkEmergencyRoutesAvailability } = emergencyModule);
} catch (error) {
  console.warn('Emergency routes not available:', error);
  preloadEmergencyRoutes = () => console.warn('Emergency routes preload not available');
  checkEmergencyRoutesAvailability = async () => false;
  const FallbackComponent = () => React.createElement('div', null, 'Emergency component not available');
  EmergencyProtocols = FallbackComponent;
  EmergencyConsultation = FallbackComponent;
  CriticalVitals = FallbackComponent;
  OfflineEmergencyProtocols = FallbackComponent;
}

export { EmergencyProtocols, EmergencyConsultation, CriticalVitals, OfflineEmergencyProtocols, preloadEmergencyRoutes, checkEmergencyRoutesAvailability };

// Patient Routes
let preloadPatientRoutes: () => void;
let getPatientRoutePriorities: () => any;
let PatientDashboard: React.ComponentType;
let MedicalHistory: React.ComponentType;
let SymptomTracker: React.ComponentType;
let MedicationManager: React.ComponentType;
let AppointmentBooking: React.ComponentType;

try {
  const patientModule = require('./PatientRoutes');
  ({ PatientDashboard, MedicalHistory, SymptomTracker, MedicationManager, AppointmentBooking, preloadPatientRoutes, getPatientRoutePriorities } = patientModule);
} catch (error) {
  console.warn('Patient routes not available:', error);
  preloadPatientRoutes = () => console.warn('Patient routes preload not available');
  getPatientRoutePriorities = () => ({});
  const FallbackComponent = () => React.createElement('div', null, 'Patient component not available');
  PatientDashboard = FallbackComponent;
  MedicalHistory = FallbackComponent;
  SymptomTracker = FallbackComponent;
  MedicationManager = FallbackComponent;
  AppointmentBooking = FallbackComponent;
}

export { PatientDashboard, MedicalHistory, SymptomTracker, MedicationManager, AppointmentBooking, preloadPatientRoutes, getPatientRoutePriorities };

// Provider Routes
let preloadProviderRoutes: () => void;
let getProviderRoutePriorities: () => any;
let checkProviderRouteAccess: () => boolean;
let ProviderDashboard: React.ComponentType;
let PatientManagement: React.ComponentType;

try {
  const providerModule = require('./ProviderRoutes');
  ({ ProviderDashboard, PatientManagement, preloadProviderRoutes, getProviderRoutePriorities, checkProviderRouteAccess } = providerModule);
} catch (error) {
  console.warn('Provider routes not available:', error);
  preloadProviderRoutes = () => console.warn('Provider routes preload not available');
  getProviderRoutePriorities = () => ({});
  checkProviderRouteAccess = () => false;
  const FallbackComponent = () => React.createElement('div', null, 'Provider component not available');
  ProviderDashboard = FallbackComponent;
  PatientManagement = FallbackComponent;
}

export { ProviderDashboard, PatientManagement, preloadProviderRoutes, getProviderRoutePriorities, checkProviderRouteAccess };

// Auth Routes
let preloadAuthRoutes: () => void;
let getDashboardForRole: (role: UserRole) => React.ComponentType;
let requiresAuthentication: () => boolean;
let LoginPage: React.ComponentType;
let RegisterPage: React.ComponentType;
let ForgotPassword: React.ComponentType;
let RoleBasedDashboard: React.ComponentType;
let WelcomeLanguageSelection: React.ComponentType;
let CountryRegionalSelection: React.ComponentType;

try {
  const authModule = require('./AuthRoutes');
  ({ LoginPage, RegisterPage, ForgotPassword, RoleBasedDashboard, WelcomeLanguageSelection, CountryRegionalSelection, preloadAuthRoutes, getDashboardForRole, requiresAuthentication } = authModule);
} catch (error) {
  console.warn('Auth routes not available:', error);
  preloadAuthRoutes = () => console.warn('Auth routes preload not available');
  getDashboardForRole = () => () => React.createElement('div', null, 'Dashboard not available');
  requiresAuthentication = () => true;
  const FallbackComponent = () => React.createElement('div', null, 'Auth component not available');
  LoginPage = FallbackComponent;
  RegisterPage = FallbackComponent;
  ForgotPassword = FallbackComponent;
  RoleBasedDashboard = FallbackComponent;
  WelcomeLanguageSelection = FallbackComponent;
  CountryRegionalSelection = FallbackComponent;
}

export { LoginPage, RegisterPage, ForgotPassword, RoleBasedDashboard, WelcomeLanguageSelection, CountryRegionalSelection, preloadAuthRoutes, getDashboardForRole, requiresAuthentication };

// =============================================================================
// ADDITIONAL SHARED COMPONENTS (Created locally)
// =============================================================================

// Additional shared components that aren't in modular files
export const MedicalCharts = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div', 
      { className: 'p-8 bg-gray-50 border border-gray-200 rounded' },
      React.createElement('h2', { className: 'text-gray-800 font-bold' }, '📊 Medical Charts'),
      React.createElement('p', { className: 'text-gray-700 mt-2' }, 'Medical charts system is loading...')
    )
  }),
  { priority: 'low' }
);

export const DataVisualization = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-gray-50 border border-gray-200 rounded' },
      React.createElement('h2', { className: 'text-gray-800 font-bold' }, '📈 Data Visualization'),
      React.createElement('p', { className: 'text-gray-700 mt-2' }, 'Data visualization tools are loading...')
    )
  }),
  { priority: 'low' }
);

export const NotificationCenter = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-blue-50 border border-blue-200 rounded' },
      React.createElement('h2', { className: 'text-blue-800 font-bold' }, '🔔 Notification Center'),
      React.createElement('p', { className: 'text-blue-700 mt-2' }, 'Notification system is loading...')
    )
  }),
  { priority: 'normal' }
);

export const HelpCenter = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-green-50 border border-green-200 rounded' },
      React.createElement('h2', { className: 'text-green-800 font-bold' }, '❓ Help Center'),
      React.createElement('p', { className: 'text-green-700 mt-2' }, 'Help documentation is loading...')
    )
  }),
  { priority: 'low' }
);

export const PrivacyPolicy = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-gray-50 border border-gray-200 rounded' },
      React.createElement('h2', { className: 'text-gray-800 font-bold' }, '🔒 Privacy Policy'),
      React.createElement('p', { className: 'text-gray-700 mt-2' }, 'Privacy policy is loading...')
    )
  }),
  { priority: 'low' }
);

// =============================================================================
// ROUTE MANAGEMENT UTILITIES
// =============================================================================

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: UserRole): void {
  console.log(`🚀 Preloading routes for role: ${userRole}`);

  switch (userRole) {
    case 'patient':
      preloadPatientRoutes();
      break;
    case 'healthcare_provider':
      preloadProviderRoutes();
      break;
    case 'emergency_responder':
      preloadEmergencyRoutes();
      break;
    case 'admin':
      // Admin routes would be preloaded here
      break;
    default:
      preloadAuthRoutes();
  }
}

/**
 * Get route loading priority based on user role and current route
 */
export function getRouteLoadingPriority(userRole: UserRole, routeName: string): 'low' | 'normal' | 'high' | 'emergency' {
  // Emergency routes always have emergency priority
  if (routeName.includes('emergency') || routeName.includes('critical')) {
    return 'emergency';
  }

  // Dashboard routes have high priority
  if (routeName.includes('dashboard')) {
    return 'high';
  }

  // Medical data routes have normal priority
  if (routeName.includes('medical') || routeName.includes('consultation')) {
    return 'normal';
  }

  // Everything else has low priority
  return 'low';
}

/**
 * Initialize all route modules
 */
export async function initializeRoutes(): Promise<void> {
  console.log('🔧 Initializing route modules...');

  try {
    // Check emergency routes availability first
    const emergencyAvailable = await checkEmergencyRoutesAvailability();
    if (!emergencyAvailable) {
      console.warn('⚠️ Emergency routes not fully available');
    }

    // Preload emergency routes immediately
    preloadEmergencyRoutes();

    console.log('✅ Route modules initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize route modules:', error);
  }
}

/**
 * Get all available routes by category
 */
export function getRoutesByCategory() {
  return {
    emergency: ['EmergencyProtocols', 'EmergencyConsultation', 'CriticalVitals'],
    patient: ['PatientDashboard', 'MedicalHistory', 'SymptomTracker', 'MedicationManager'],
    provider: ['ProviderDashboard', 'PatientManagement', 'ConsultationInterface', 'MedicalRecords'],
    auth: ['LoginPage', 'RegisterPage', 'RoleBasedDashboard'],
    shared: ['MedicalCharts', 'DataVisualization', 'NotificationCenter', 'HelpCenter']
  };
}

// =============================================================================
// ADDITIONAL COMPONENTS (Not in modular files)
// =============================================================================

export const ConsultationInterface = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-blue-50 border border-blue-200 rounded' },
      React.createElement('h2', { className: 'text-blue-800 font-bold' }, '🩺 Consultation Interface'),
      React.createElement('p', { className: 'text-blue-700 mt-2' }, 'Consultation interface is loading...')
    )
  }),
  { priority: 'high' }
);

export const MedicalRecords = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-green-50 border border-green-200 rounded' },
      React.createElement('h2', { className: 'text-green-800 font-bold' }, '📋 Medical Records'),
      React.createElement('p', { className: 'text-green-700 mt-2' }, 'Medical records system is loading...')
    )
  }),
  { priority: 'normal' }
);

export const DiagnosisTools = createLazyComponent(
  () => Promise.resolve({
    default: () => React.createElement('div',
      { className: 'p-8 bg-purple-50 border border-purple-200 rounded' },
      React.createElement('h2', { className: 'text-purple-800 font-bold' }, '🔬 Diagnosis Tools'),
      React.createElement('p', { className: 'text-purple-700 mt-2' }, 'Diagnostic tools are loading...')
    )
  }),
  { priority: 'normal' }
);

// =============================================================================
// ROUTE UTILITIES
// =============================================================================

/**
 * Get all available routes by category for the main orchestrator
 */
export function getAvailableRoutes() {
  return {
    emergency: ['EmergencyProtocols', 'EmergencyConsultation', 'CriticalVitals'],
    patient: ['PatientDashboard', 'MedicalHistory', 'SymptomTracker', 'MedicationManager'],
    provider: ['ProviderDashboard', 'PatientManagement', 'ConsultationInterface', 'MedicalRecords'],
    auth: ['LoginPage', 'RegisterPage', 'RoleBasedDashboard'],
    shared: ['MedicalCharts', 'DataVisualization', 'NotificationCenter', 'HelpCenter']
  };
}

/**
 * Initialize route system
 */
export async function initializeRouteSystem(): Promise<void> {
  console.log('🚀 Initializing VoiceHealth AI route system...');

  try {
    // Preload emergency routes immediately
    await preloadEmergencyRoutes();
    console.log('✅ Route system initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize route system:', error);
  }
}
